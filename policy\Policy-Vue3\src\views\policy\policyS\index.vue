<template>
  <div class="home-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>企业就业服务平台</h1>
      <p>欢迎使用企业就业服务平台，您可以在这里查看和申请各类政策补贴</p>
    </div>

    <!-- 政策申请列表 -->
    <div class="policy-list">
      <h2>可申请政策列表</h2>
      <div class="policy-items" v-loading="loading">
        <div v-for="policy in policyList" :key="policy.policyId" class="policy-item" :class="{
          'has-application': policy.userApplication,
          'application-approved': ['1', '4', '6'].includes(policy.applicationStatus),
          'application-rejected': ['2', '5'].includes(policy.applicationStatus),
          'application-pending': ['0', '3'].includes(policy.applicationStatus)
        }">
          <div class="policy-content">
            <div class="policy-header">
              <h3 class="policy-title">{{ policy.policyName }}</h3>
              <el-tag :type="getTypeTagType(policy.policyType)" size="small">
                {{ policy.policyType }}
              </el-tag>
            </div>

            <p class="policy-description">{{ policy.policyDescription }}</p>

            <div class="policy-info">
              <div class="info-row">
                <div class="info-item">
                  <span class="label">生效日期：</span>
                  <span class="value">{{ formatDate(policy.effectiveDate) }}</span>
                </div>
                <div class="info-item" v-if="policy.expiryDate">
                  <span class="label">失效日期：</span>
                  <span class="value">{{ formatDate(policy.expiryDate) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">政策状态：</span>
                  <el-tag :type="policy.status === '0' ? 'success' : 'danger'" size="small">
                    {{ policy.status === '0' ? '正常' : '停用' }}
                  </el-tag>
                </div>
              </div>

              <div class="info-row" v-if="policy.userApplication">
                <div class="info-item">
                  <span class="label">申请状态：</span>
                  <el-tag :type="getApplicationStatusTagType(policy.applicationStatus)" size="small">
                    {{ getApplicationStatusText(policy.applicationStatus) }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <span class="label">申请时间：</span>
                  <span class="value">{{ formatDate(policy.userApplication.submitTime) }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="policy-actions">
            <!-- 未申请过或申请被拒绝，可以申请 -->
            <el-button v-if="policy.canApply" type="primary" size="default" @click="handleApply(policy)"
              :disabled="policy.status !== '0'" class="action-btn primary-btn">
              <el-icon>
                <Plus />
              </el-icon>
              {{ policy.userApplication ? '重新申请' : '立即申请' }}
            </el-button>

            <!-- 已申请，显示申请状态 -->
            <el-button v-else-if="policy.userApplication" :type="getApplicationButtonType(policy.applicationStatus)"
              size="default" disabled class="status-btn">
              <el-icon>
                <InfoFilled />
              </el-icon>
              {{ getApplicationStatusText(policy.applicationStatus) }}
            </el-button>

            <!-- 查看申请材料 -->
            <el-button v-if="policy.userApplication" type="success" size="default"
              @click="handleViewMaterials(policy.userApplication)" class="action-btn view-btn">
              <el-icon>
                <View />
              </el-icon>
              查看申请
            </el-button>

            <!-- 查看审核状态 -->
            <el-button v-if="policy.userApplication" type="info" size="default"
              @click="handleViewApprovalRecords(policy.userApplication)" class="action-btn review-btn">
              <el-icon>
                <Clock />
              </el-icon>
              审核状态
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 申请弹窗 -->
    <el-dialog v-model="applyDialogVisible" :title="`申请 - ${currentPolicy?.policyName}`" width="800px"
      :close-on-click-modal="false" append-to-body>
      <div class="apply-form">
        <el-form ref="applyFormRef" :model="applyForm" :rules="applyRules" label-width="120px">
          <el-form-item label="申请政策" prop="policyId">
            <el-input v-model="currentPolicy.policyName" disabled />
          </el-form-item>

          <el-form-item label="申请人姓名" prop="applicantName">
            <el-input v-model="applyForm.applicantName" placeholder="请输入申请人姓名" />
          </el-form-item>

          <el-form-item label="联系电话" prop="applicantPhone">
            <el-input v-model="applyForm.applicantPhone" placeholder="请输入联系电话" />
          </el-form-item>

          <el-form-item label="备注" prop="remark">
            <el-input v-model="applyForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>

          <el-divider content-position="left">所需材料</el-divider>

          <div class="required-materials">
            <div class="material-item" v-for="(material, index) in requiredMaterials" :key="index">
              <div class="material-header">
                <div class="material-info">
                  <el-icon class="material-icon">
                    <Document />
                  </el-icon>
                  <span class="material-name">{{ material.name }}</span>
                  <div class="material-tags">
                    <el-tag v-if="material.required" type="danger" size="small">必需</el-tag>
                    <el-tag v-else type="info" size="small">可选</el-tag>
                  </div>
                </div>
              </div>

              <div class="material-upload">
                <FileUpload v-model:value="material.files" :limit="5" :file-size="0" :file-type="[]"
                  :is-show-tip="false" @fileLoad="(data) => handleFileLoad(data, index)" />
              </div>
            </div>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="applyDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmitApply">
            提交申请
          </el-button>
        </div>
      </template>
    </el-dialog>



    <!-- 材料查看弹窗 -->
    <MaterialsDialog ref="materialsDialogRef" />

    <!-- 审核记录弹窗 -->
    <ApprovalRecordsDialog ref="approvalRecordsDialogRef" />
  </div>
</template>

<script setup name="Home">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { listPolicyInfo } from "@/api/policy/info"
import { addPolicyApplication, listMyApplications } from "@/api/policy/application"
import { parseTime } from "@/utils/ruoyi"
import { Document, Plus, InfoFilled, View, Clock } from '@element-plus/icons-vue'
import FileUpload from '@/components/FileUpload/index.vue'
import MaterialsDialog from '../policyPlan/MaterialsDialog.vue'
import ApprovalRecordsDialog from '../policyPlan/ApprovalRecordsDialog.vue'

const { proxy } = getCurrentInstance()

// 响应式数据
const loading = ref(false)
const policyList = ref([])
const myApplications = ref([])
const applyDialogVisible = ref(false)
const currentPolicy = ref(null)
const submitLoading = ref(false)
const applyFormRef = ref(null)
const materialsDialogRef = ref(null)
const approvalRecordsDialogRef = ref(null)

// 申请表单
const applyForm = reactive({
  policyId: '',
  applicantName: '',
  applicantPhone: '',
  remark: ''
})

// 表单验证规则
const applyRules = {
  applicantName: [
    { required: true, message: '请输入申请人姓名', trigger: 'blur' }
  ],
  applicantPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 所需材料列表
const requiredMaterials = ref([
  {
    name: '《企业新增岗位吸纳就业困难人员、高校毕业生和退役军人社保补贴申领表》',
    required: true,
    files: []
  },
  {
    name: '企业的营业执照副本复印件',
    required: true,
    files: []
  },
  {
    name: '退役军人需提供退伍证原件及复印件',
    required: false,
    files: []
  },
  {
    name: '企业社保缴费凭证',
    required: true,
    files: []
  },
  {
    name: '企业在银行开立的基本账户',
    required: true,
    files: []
  }
])

// 页面初始化
onMounted(() => {
  initPageData()
})

// 初始化页面数据
const initPageData = async () => {
  loading.value = true
  try {
    // 并行获取政策列表和用户申请记录
    const [policyResponse, applicationResponse] = await Promise.all([
      listPolicyInfo({ status: '0' }), // 只查询正常状态的政策
      listMyApplications({}) // 获取当前用户的申请记录
    ])

    const policies = policyResponse.rows || []
    const applications = applicationResponse.rows || []

    // 为每个政策添加申请状态信息
    policyList.value = policies.map(policy => {
      const userApplication = applications.find(app => app.policyId === policy.policyId)
      return {
        ...policy,
        userApplication: userApplication || null,
        applicationStatus: userApplication ? userApplication.applicationStatus : null,
        canApply: !userApplication || ['2', '5'].includes(userApplication.applicationStatus) // 可以申请：未申请过 或 初审拒绝 或 终审拒绝
      }
    })

    myApplications.value = applications
  } catch (error) {
    console.error('获取页面数据失败:', error)
    proxy?.$modal?.msgError('获取页面数据失败')
  } finally {
    loading.value = false
  }
}



// 获取政策类型标签类型
const getTypeTagType = (type) => {
  const typeMap = {
    '就业扶持': 'success',
    '创业支持': 'warning',
    '技能培训': 'info',
    '社会保障': 'primary',
    '其他': 'default'
  }
  return typeMap[type] || 'default'
}

// 获取申请状态标签类型
const getApplicationStatusTagType = (status) => {
  const statusMap = {
    '0': 'warning',  // 待初审
    '1': 'success',  // 初审通过
    '2': 'danger',   // 初审拒绝
    '3': 'warning',  // 待终审
    '4': 'success',  // 终审通过
    '5': 'danger',   // 终审拒绝
    '6': 'success'   // 已完成
  }
  return statusMap[status] || 'info'
}

// 获取申请状态文本
const getApplicationStatusText = (status) => {
  const statusMap = {
    '0': '待初审',
    '1': '初审通过',
    '2': '初审拒绝',
    '3': '待终审',
    '4': '终审通过',
    '5': '终审拒绝',
    '6': '已完成'
  }
  return statusMap[status] || '未知状态'
}

// 获取申请按钮类型
const getApplicationButtonType = (status) => {
  const buttonMap = {
    '0': 'warning',  // 待初审
    '1': 'success',  // 初审通过
    '2': 'danger',   // 初审拒绝
    '3': 'warning',  // 待终审
    '4': 'success',  // 终审通过
    '5': 'danger',   // 终审拒绝
    '6': 'success'   // 已完成
  }
  return buttonMap[status] || 'info'
}

// 格式化日期
const formatDate = (date) => {
  return date ? parseTime(date, '{y}-{m}-{d}') : '-'
}

// 处理申请操作
const handleApply = (policy) => {
  currentPolicy.value = policy
  applyForm.policyId = policy.policyId
  resetMaterials()
  initMaterials()
  applyDialogVisible.value = true
}



// 查看申请材料
const handleViewMaterials = (application) => {
  materialsDialogRef.value?.openDialog(application)
}

// 查看审核记录
const handleViewApprovalRecords = (application) => {
  approvalRecordsDialogRef.value?.openDialog(application.applicationId)
}

// 重置材料上传状态
const resetMaterials = () => {
  requiredMaterials.value.forEach(material => {
    material.files = []
  })
}

// 初始化材料状态
const initMaterials = () => {
  requiredMaterials.value.forEach(material => {
    if (!material.files) {
      material.files = []
    }
  })
}

// 文件上传处理
const handleFileLoad = (data, index) => {
  requiredMaterials.value[index].files = data.fileList || []
}

// 提交申请
const handleSubmitApply = async () => {
  if (!applyFormRef.value) return

  try {
    await applyFormRef.value.validate()

    // 检查必需材料是否已上传
    const requiredNotUploaded = requiredMaterials.value.filter(m => m.required && (!m.files || m.files.length === 0))
    if (requiredNotUploaded.length > 0) {
      proxy?.$modal?.msgError('请上传所有必需的材料文件')
      return
    }

    submitLoading.value = true

    // 准备申请数据
    const applicationData = {
      policyId: applyForm.policyId,
      applicantName: applyForm.applicantName,
      applicantPhone: applyForm.applicantPhone,
      // applicantUserId 由后端根据当前登录用户自动设置
      requiredMaterials: JSON.stringify(requiredMaterials.value),
      remark: applyForm.remark
    }

    await addPolicyApplication(applicationData)

    proxy?.$modal?.msgSuccess('申请提交成功，请等待审核')
    applyDialogVisible.value = false
    resetForm()
    // 重新加载页面数据以更新申请状态
    initPageData()
  } catch (error) {
    console.error('提交申请失败:', error)
    proxy?.$modal?.msgError('提交申请失败')
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  applyForm.policyId = ''
  applyForm.applicantName = ''
  applyForm.applicantPhone = ''
  applyForm.remark = ''
  resetMaterials()
  if (applyFormRef.value) {
    applyFormRef.value.resetFields()
  }
}
</script>

<style scoped>
.home-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.policy-list h2 {
  color: #303133;
  margin-bottom: 20px;
  font-size: 1.5rem;
  font-weight: 600;
}

.policy-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.policy-item {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.policy-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.policy-content {
  flex: 1;
  margin-right: 20px;
}

.policy-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.policy-title {
  font-weight: 600;
  color: #303133;
  font-size: 1.2rem;
  margin: 0;
}

.policy-description {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 16px;
  font-size: 0.95rem;
}

.policy-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-row {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  min-width: 150px;
}

.info-item .label {
  color: #909399;
  min-width: 80px;
  font-weight: 500;
}

.info-item .value {
  color: #303133;
}

.policy-actions {
  display: flex;
  flex-direction: row;
  gap: 8px;
  min-width: 300px;
  align-items: center;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.action-btn {
  min-width: 90px;
  height: 36px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 13px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 0 12px;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.primary-btn {
  background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
  border: none;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

.primary-btn:hover {
  background: linear-gradient(135deg, #337ecc 0%, #2b6cb0 100%);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.status-btn {
  font-weight: 600;
  cursor: not-allowed;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.view-btn {
  background: linear-gradient(135deg, #67c23a 0%, #529b2e 100%);
  border: none;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

.view-btn:hover {
  background: linear-gradient(135deg, #529b2e 0%, #3e7b1f 100%);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.review-btn {
  background: linear-gradient(135deg, #909399 0%, #73767a 100%);
  border: none;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

.review-btn:hover {
  background: linear-gradient(135deg, #73767a 0%, #5a5e63 100%);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 申请状态相关样式 */
.policy-item.has-application {
  border-left: 4px solid #409eff;
}

.policy-item.application-approved {
  border-left: 4px solid #67c23a;
}

.policy-item.application-rejected {
  border-left: 4px solid #f56c6c;
}

.policy-item.application-pending {
  border-left: 4px solid #e6a23c;
}

/* 申请弹窗样式 */
.apply-form {
  max-height: 70vh;
  overflow-y: auto;
}

.required-materials {
  margin-top: 10px;
}

.material-item {
  display: flex;
  flex-direction: column;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 16px;
  background-color: #fafafa;
  gap: 12px;
}

.material-header {
  width: 100%;
}

.material-info {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.material-icon {
  color: #409eff;
  font-size: 18px;
  flex-shrink: 0;
}

.material-name {
  color: #303133;
  font-weight: 500;
  flex: 1;
  line-height: 1.4;
}

.material-tags {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.material-upload {
  width: 100%;
  padding-left: 30px;
  /* 对齐图标 */
}

/* 政策详情弹窗样式 */
.policy-detail {
  padding: 10px 0;
}

.detail-item {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;
}

.detail-item .label {
  color: #909399;
  min-width: 100px;
  font-weight: 500;
  margin-right: 10px;
}

.detail-item .value {
  color: #303133;
  flex: 1;
}

.detail-item .description {
  line-height: 1.6;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .policy-item {
    flex-direction: column;
    gap: 16px;
  }

  .policy-content {
    margin-right: 0;
  }

  .policy-actions {
    min-width: auto;
    width: 100%;
    justify-content: center;
    gap: 6px;
  }

  .action-btn {
    flex: 1;
    min-width: 80px;
    height: 32px;
    font-size: 12px;
    padding: 0 8px;
  }

  .info-row {
    flex-direction: column;
    gap: 8px;
  }

  .info-item {
    min-width: auto;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .material-upload {
    padding-left: 0;
  }
}

/* 滚动条样式 */
.apply-form::-webkit-scrollbar {
  width: 6px;
}

.apply-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.apply-form::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.apply-form::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>