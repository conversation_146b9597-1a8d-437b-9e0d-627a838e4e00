<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="800px" :close-on-click-modal="false"
    :close-on-press-escape="false" append-to-body>
    <div class="approval-records-container">
      <el-timeline v-if="records.length > 0">
        <el-timeline-item v-for="(record, index) in records" :key="record.recordId"
          :timestamp="parseTime(record.approvalTime)" placement="top" :type="getTimelineType(record.approvalStatus)"
          :icon="getTimelineIcon(record.approvalStatus)">
          <el-card class="record-card">
            <div class="record-header">
              <div class="record-level">
                <el-tag :type="getLevelTagType(record.approvalLevel)" size="small">
                  {{ getLevelText(record.approvalLevel) }}
                </el-tag>
              </div>
              <div class="record-status">
                <el-tag :type="getStatusTagType(record.approvalStatus)" size="small">
                  {{ getStatusText(record.approvalStatus) }}
                </el-tag>
              </div>
            </div>

            <div class="record-content">
              <div class="record-info">
                <div class="info-item" v-if="record.approverUserName">
                  <span class="label">审批人：</span>
                  <span class="value">{{ record.approverUserName }}</span>
                </div>
                <div class="info-item" v-if="record.approvalTime">
                  <span class="label">审批时间：</span>
                  <span class="value">{{ parseTime(record.approvalTime) }}</span>
                </div>
              </div>

              <div class="record-comment" v-if="record.approvalComment">
                <div class="comment-label">审批意见：</div>
                <div class="comment-content">{{ record.approvalComment }}</div>
              </div>

              <div class="record-files" v-if="record.approvalFiles && record.approvalFiles.length > 0">
                <div class="files-label">相关文件：</div>
                <div class="files-list">
                  <el-tag v-for="file in record.approvalFiles" :key="file.id" type="info" size="small" class="file-tag">
                    {{ file.name }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>

      <el-empty v-else description="暂无审核记录" />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import { getApprovalRecords } from "@/api/policy/application"
import { parseTime } from "@/utils/ruoyi"
import { Clock, Check, Close, Warning } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance()

const dialogVisible = ref(false)
const dialogTitle = ref('审核记录')
const records = ref([])
const loading = ref(false)

// 打开弹窗
const openDialog = async (applicationId) => {
  dialogVisible.value = true
  dialogTitle.value = '审核记录'
  loading.value = true

  try {
    const response = await getApprovalRecords(applicationId)
    records.value = response.data || []
  } catch (error) {
    console.error('获取审核记录失败:', error)
    proxy.$modal.msgError('获取审核记录失败')
    records.value = []
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  records.value = []
}

// 获取时间线类型
const getTimelineType = (status) => {
  const typeMap = {
    '0': 'warning',  // 待审批
    '1': 'success',  // 审批通过
    '2': 'danger'    // 审批拒绝
  }
  return typeMap[status] || 'info'
}

// 获取时间线图标
const getTimelineIcon = (status) => {
  const iconMap = {
    '0': Clock,      // 待审批
    '1': Check,      // 审批通过
    '2': Close       // 审批拒绝
  }
  return iconMap[status] || Warning
}

// 获取审批层级标签类型
const getLevelTagType = (level) => {
  const typeMap = {
    1: 'primary',    // 初审
    2: 'warning'     // 终审
  }
  return typeMap[level] || 'info'
}

// 获取审批层级文本
const getLevelText = (level) => {
  const textMap = {
    1: '初审',
    2: '终审'
  }
  return textMap[level] || '未知层级'
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    '0': 'warning',  // 待审批
    '1': 'success',  // 审批通过
    '2': 'danger'    // 审批拒绝
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    '0': '待审批',
    '1': '审批通过',
    '2': '审批拒绝'
  }
  return textMap[status] || '未知状态'
}

// 暴露方法给父组件
defineExpose({
  openDialog
})
</script>

<style scoped>
.approval-records-container {
  max-height: 500px;
  overflow-y: auto;
}

.record-card {
  margin-bottom: 16px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.record-content {
  color: #606266;
}

.record-info {
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.label {
  font-weight: 500;
  color: #909399;
  min-width: 80px;
}

.value {
  color: #303133;
}

.record-comment {
  margin-bottom: 12px;
}

.comment-label {
  font-weight: 500;
  color: #909399;
  margin-bottom: 8px;
}

.comment-content {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  line-height: 1.6;
  color: #303133;
}

.record-files {
  margin-top: 12px;
}

.files-label {
  font-weight: 500;
  color: #909399;
  margin-bottom: 8px;
}

.files-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.file-tag {
  cursor: pointer;
}

.file-tag:hover {
  opacity: 0.8;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 12px;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 28px;
}
</style>
