// 公用模块html
headerBar()
footerBar()

// 培训订单数据存储
var trainingOrderData = [];

// 跳转到培训订单详情页
function goTrainingOrderDetail(orderId) {
    window.open('trainingOrderDetail.html?id=' + orderId);
}

// 跳转到培训订单列表页
function goTrainingOrderList() {
    window.open('trainingOrderList.html');
}

// 跳转到机构申请页面
function goInstitutionApply(orderId) {
    // window.open('http://localhost:81/#/order/application/institution?orderId=' + orderId);
    window.open('http://************/admin/#/order/application/institution?orderId=' + orderId);
}

// 跳转到培训报名页面（先跳转到详情页）
function goTrainingSignup(orderId) {
    window.open('trainingOrderDetail.html?id=' + orderId);
}

// 培训订单原生Ajax请求函数
function trainingOrderAjaxRequest(url, params, callback) {
    var baseUrl = 'http://************/sux-admin/';
    // var baseUrl = 'http://localhost:80/sux-admin/';

    // 构建查询参数
    var queryString = '';
    if (params && typeof params === 'object') {
        var paramArray = [];
        for (var key in params) {
            if (params.hasOwnProperty(key) && params[key] !== null && params[key] !== undefined) {
                paramArray.push(encodeURIComponent(key) + '=' + encodeURIComponent(params[key]));
            }
        }
        queryString = paramArray.length > 0 ? '?' + paramArray.join('&') : '';
    }

    var xhr = new XMLHttpRequest();
    xhr.open('GET', baseUrl + url + queryString, true);
    xhr.timeout = 30000;
    xhr.setRequestHeader('Content-Type', 'application/json');

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (callback && typeof callback === 'function') {
                        callback(response);
                    }
                } catch (e) {
                    console.error('解析响应数据失败:', e);
                    if (callback && typeof callback === 'function') {
                        callback({
                            code: -1,
                            msg: '解析响应数据失败',
                            rows: [],
                            total: 0
                        });
                    }
                }
            } else {
                console.error('培训订单请求失败:', xhr.status, xhr.statusText);
                if (callback && typeof callback === 'function') {
                    callback({
                        code: -1,
                        msg: '请求失败: ' + xhr.status + ' ' + xhr.statusText,
                        rows: [],
                        total: 0
                    });
                }
            }
        }
    };

    xhr.ontimeout = function() {
        console.error('培训订单请求超时');
        if (callback && typeof callback === 'function') {
            callback({
                code: -1,
                msg: '请求超时',
                rows: [],
                total: 0
            });
        }
    };

    xhr.onerror = function() {
        console.error('培训订单请求发生错误');
        if (callback && typeof callback === 'function') {
            callback({
                code: -1,
                msg: '网络错误',
                rows: [],
                total: 0
            });
        }
    };

    xhr.send();
}
// 渲染培训订单列表到页面
function renderTrainingOrderList(orderData) {
    var $orderList = $('.trainingOrderList');
    $orderList.empty(); // 清空现有内容

    if (!orderData || orderData.length === 0) {
        $('.trainingOrderOut').hide();
        $('.nodataPic3').show();
        return;
    }

    // 遍历数据生成HTML
    orderData.forEach(function(order) {
        var orderHtml = createOrderItemHtml(order);
        $orderList.append(orderHtml);
    });

    $('.trainingOrderOut').show();
    $('.nodataPic3').hide();

    // 更新侧边栏统计数据
    updateSidebarStats(orderData);

    // 更新底部导航信息
    updateBottomNavigation(orderData);

    console.log('培训订单列表渲染完成，共', orderData.length, '条数据');
}

// 创建单个订单项的HTML
function createOrderItemHtml(order) {
    // 处理培训费用显示
    var feeText = order.trainingFee ? '￥' + order.trainingFee : '免费';

    // 处理培训时间显示
    var timeText = '--';
    if (order.startDate && order.endDate) {
        var startDate = order.startDate.split(' ')[0];
        var endDate = order.endDate.split(' ')[0];
        timeText = startDate + ' 至 ' + endDate;
    }

    // 处理参与人数
    var participantsText = (order.currentParticipants || 0) + '/' + (order.maxParticipants || 0) + '人';

    var html = `
        <li class="pr transi">
            <div class="order-content">
                <div class="orderHeader clearfix">
                    <p class="orderTitle transi paraoverflow2" title="${order.orderTitle || ''}">${order.orderTitle || '--'}</p>
                    <span class="orderFee fr">${feeText}</span>
                </div>
                <p class="textoverflow orderType">培训类型：<span>${order.trainingType || '--'}</span></p>
                <p class="textoverflow orderDuration">培训时长：<span>${order.trainingDuration ? order.trainingDuration + '小时' : '--'}</span></p>
                <p class="textoverflow orderTime">培训时间：<span>${timeText}</span></p>
                <p class="textoverflow orderAddress">培训地址：<span>${order.trainingAddress || '--'}</span></p>
                <div class="clearfix orderFooter">
                    <span class="fl orderParticipants">${participantsText}</span>
                    <div class="fr orderActions">
                        <button class="orderBtn institutionBtn" onclick="goInstitutionApply(${order.orderId})">
                            <span>机构招募</span>
                        </button>
                        <button class="orderBtn trainingBtn" onclick="goTrainingSignup(${order.orderId})">
                            <span>培训报名</span>
                        </button>
                    </div>
                </div>
            </div>
        </li>
    `;

    return html;
}

// 加载首页培训订单列表
function loadTrainingOrderList() {
    var obj = {
        pageSize: 5,  // 首页只显示5条
        pageNum: 1,
        orderStatus: '1' // 只显示已发布的订单
    };

    console.log('首页培训订单请求参数:', obj);

    trainingOrderAjaxRequest('public/training/order/list', obj, function(data){
        console.log('首页培训订单API响应:', data);

        if(data.code == 0 || data.code == 200) {
            var rows = data.rows || data.data || [];
            console.log('获取到的培训订单数据:', rows);

            // 存储数据并渲染
            trainingOrderData = rows;
            renderTrainingOrderList(rows);
        } else {
            console.error('首页获取培训订单列表失败：', data.msg || data.message);

            // 如果API失败，加载模拟数据用于测试
            loadMockTrainingOrders();
        }
    });
}

// 模拟培训订单数据用于测试
function loadMockTrainingOrders() {
    console.log('加载模拟培训订单数据');
    var mockData = [
        {
            orderId: 9,
            orderTitle: "企业管理与领导力提升",
            orderDescription: "针对中高层管理人员的综合管理能力提升培训，涵盖团队管理、战略规划、沟通技巧等",
            trainingType: "管理培训",
            trainingCategory: "领导力",
            trainingLevel: "中级",
            trainingDuration: 40,
            maxParticipants: 20,
            currentParticipants: 12,
            trainingFee: 4500.00,
            trainingAddress: "西宁市市北区辽宁路263号",
            contactPerson: "王老师",
            contactPhone: "13800138003",
            contactEmail: "<EMAIL>",
            startDate: "2025-07-30 09:00:00",
            endDate: "2025-08-01 18:00:00",
            registrationDeadline: "2025-07-25 23:59:59",
            orderStatus: "1",
            isFeatured: "1",
            requirements: "具备3年以上管理经验或相关工作背景",
            certificateInfo: "完成培训可获得企业管理师认证证书"
        },
        {
            orderId: 8,
            orderTitle: "人工智能与机器学习入门",
            orderDescription: "零基础学习人工智能和机器学习基础知识，包括Python编程、数据处理、算法原理等",
            trainingType: "技术培训",
            trainingCategory: "IT技能",
            trainingLevel: "初级",
            trainingDuration: 120,
            maxParticipants: 40,
            currentParticipants: 25,
            trainingFee: 5200.00,
            trainingAddress: "西宁市高新区智力岛路1号",
            contactPerson: "周老师",
            contactPhone: "13800138008",
            contactEmail: "<EMAIL>",
            startDate: "2025-08-20 09:00:00",
            endDate: "2025-09-05 18:00:00",
            registrationDeadline: "2025-08-15 23:59:59",
            orderStatus: "1",
            isFeatured: "1",
            requirements: "理工科背景，具备基础数学知识",
            certificateInfo: "完成培训可获得AI工程师入门认证证书"
        },
        {
            orderId: 7,
            orderTitle: "财务分析与预算管理",
            orderDescription: "深入学习财务分析方法、预算编制与控制、成本管理等财务管理核心技能",
            trainingType: "管理培训",
            trainingCategory: "财务管理",
            trainingLevel: "中级",
            trainingDuration: 50,
            maxParticipants: 25,
            currentParticipants: 15,
            trainingFee: 3500.00,
            trainingAddress: "西宁市市南区山东路9号",
            contactPerson: "孙老师",
            contactPhone: "13800138007",
            contactEmail: "<EMAIL>",
            startDate: "2025-08-18 09:00:00",
            endDate: "2025-08-22 18:00:00",
            registrationDeadline: "2025-08-12 23:59:59",
            orderStatus: "1",
            isFeatured: "0",
            requirements: "具备财务基础知识，有一定工作经验",
            certificateInfo: "完成培训可获得财务分析师认证证书"
        },
        {
            orderId: 6,
            orderTitle: "项目管理PMP认证培训",
            orderDescription: "系统学习项目管理知识体系，准备PMP认证考试，提升项目管理专业能力",
            trainingType: "管理培训",
            trainingCategory: "项目管理",
            trainingLevel: "高级",
            trainingDuration: 80,
            maxParticipants: 15,
            currentParticipants: 8,
            trainingFee: 6800.00,
            trainingAddress: "西宁市崂山区科技街36号",
            contactPerson: "赵老师",
            contactPhone: "13800138006",
            contactEmail: "<EMAIL>",
            startDate: "2025-08-15 09:00:00",
            endDate: "2025-08-25 18:00:00",
            registrationDeadline: "2025-08-10 23:59:59",
            orderStatus: "1",
            isFeatured: "1",
            requirements: "具备3年以上项目管理经验，大学本科以上学历",
            certificateInfo: "完成培训可获得PMP认证培训证书"
        },
        {
            orderId: 5,
            orderTitle: "前端开发技能提升",
            orderDescription: "学习最新的前端开发技术，包括Vue3、React、TypeScript等现代前端框架",
            trainingType: "技术培训",
            trainingCategory: "IT技能",
            trainingLevel: "中级",
            trainingDuration: 100,
            maxParticipants: 28,
            currentParticipants: 18,
            trainingFee: 4200.00,
            trainingAddress: "西宁市市南区东海西路15号",
            contactPerson: "陈老师",
            contactPhone: "13800138005",
            contactEmail: "<EMAIL>",
            startDate: "2025-08-12 09:00:00",
            endDate: "2025-08-20 18:00:00",
            registrationDeadline: "2025-08-08 23:59:59",
            orderStatus: "1",
            isFeatured: "1",
            requirements: "具备HTML、CSS、JavaScript基础知识",
            certificateInfo: "完成培训可获得前端开发工程师认证证书"
        }
    ];

    // 存储模拟数据并渲染
    trainingOrderData = mockData;
    renderTrainingOrderList(mockData);
    console.log('模拟培训订单数据已设置并渲染，共', mockData.length, '条数据');
}

// 更新侧边栏统计数据
function updateSidebarStats(orderData) {
    if (!orderData || orderData.length === 0) {
        $('#totalOrders').text('0');
        $('#ongoingOrders').text('0');
        $('#totalParticipants').text('0');
        $('#completedOrders').text('0');
        // 同时更新底部统计
        $('#bottomTotalOrders').text('0');
        $('#bottomOngoingOrders').text('0');
        $('#bottomTotalParticipants').text('0');
        return;
    }

    var totalOrders = orderData.length;
    var ongoingOrders = orderData.filter(order => order.orderStatus === '2').length;
    var completedOrders = orderData.filter(order => order.orderStatus === '3').length;
    var totalParticipants = orderData.reduce((sum, order) => sum + (order.currentParticipants || 0), 0);

    // 添加动画效果 - 侧边栏
    animateNumber('#totalOrders', totalOrders);
    animateNumber('#ongoingOrders', ongoingOrders);
    animateNumber('#totalParticipants', totalParticipants);
    animateNumber('#completedOrders', completedOrders);

    // 添加动画效果 - 底部条
    animateNumber('#bottomTotalOrders', totalOrders);
    animateNumber('#bottomOngoingOrders', ongoingOrders);
    animateNumber('#bottomTotalParticipants', totalParticipants);
}

// 数字动画效果
function animateNumber(selector, targetNumber) {
    var $element = $(selector);
    var currentNumber = 0;
    var increment = Math.ceil(targetNumber / 20);

    var timer = setInterval(function() {
        currentNumber += increment;
        if (currentNumber >= targetNumber) {
            currentNumber = targetNumber;
            clearInterval(timer);
        }
        $element.text(currentNumber);
    }, 50);
}

// 按类型筛选培训
function filterByType(type) {
    window.open('trainingOrderList.html?type=' + encodeURIComponent(type));
}

// 显示即将开始的培训
function showUpcoming() {
    window.open('trainingOrderList.html?filter=upcoming');
}

// 更新底部导航信息
function updateBottomNavigation(orderData) {
    var totalCount = orderData ? orderData.length : 0;
    var currentTime = new Date();
    var timeString = currentTime.getFullYear() + '-' +
                    String(currentTime.getMonth() + 1).padStart(2, '0') + '-' +
                    String(currentTime.getDate()).padStart(2, '0') + ' ' +
                    String(currentTime.getHours()).padStart(2, '0') + ':' +
                    String(currentTime.getMinutes()).padStart(2, '0');

    $('#totalCount').text(totalCount);
    $('#lastUpdate').text(timeString);
}

// 刷新数据
function refreshData() {
    console.log('刷新培训订单数据...');

    // 显示加载状态
    $('#totalCount').text('加载中...');
    $('#lastUpdate').text('刷新中...');

    // 重新加载数据
    loadTrainingOrderList();

    // 显示刷新成功提示
    setTimeout(function() {
        showToast('数据刷新成功！', 'success');
    }, 500);
}

// 滚动到顶部
function scrollToTop() {
    $('html, body').animate({
        scrollTop: 0
    }, 800);
}

// 显示提示消息
function showToast(message, type) {
    var toastClass = type === 'success' ? 'toast-success' : 'toast-info';
    var toast = $('<div class="toast ' + toastClass + '">' + message + '</div>');

    $('body').append(toast);

    setTimeout(function() {
        toast.addClass('show');
    }, 100);

    setTimeout(function() {
        toast.removeClass('show');
        setTimeout(function() {
            toast.remove();
        }, 300);
    }, 2000);
}

// 绑定侧边栏事件
function bindSidebarEvents() {
    // 热门类型标签点击事件
    $('.type-tag').off('click').on('click', function() {
        var type = $(this).text();
        filterByType(type);
    });
}

// 绑定"更多"按钮点击事件
function bindMoreButtonEvent() {
    $('.moreBtn').off('click').on('click', function() {
        goTrainingOrderList();
    });
}

// 初始化页面
function initPage() {
    console.log('开始初始化页面...');

    // 绑定事件
    bindMoreButtonEvent();
    bindSidebarEvents();

    // 加载培训订单列表
    loadTrainingOrderList();

    console.log('页面初始化完成');
}

// 页面加载完成后初始化
$(document).ready(function() {
    initPage();
});