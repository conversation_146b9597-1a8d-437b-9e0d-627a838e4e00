/*
 Navicat Premium Data Transfer

 Source Server         : 8.0.40_3306
 Source Server Type    : MySQL
 Source Server Version : 80041
 Source Host           : localhost:3306
 Source Schema         : bussine-web-policy

 Target Server Type    : MySQL
 Target Server Version : 80041
 File Encoding         : 65001

 Date: 24/07/2025 00:09:33
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for employment_info
-- ----------------------------
DROP TABLE IF EXISTS `employment_info`;
CREATE TABLE `employment_info`  (
  `employment_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '用工信息ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用工标题',
  `employment_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用工类型（日结/周结/月结/计件）',
  `work_category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工作类别（服务员/保洁/搬运工/销售/厨师助手/快递员/保安等）',
  `work_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工作地点',
  `region_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区域代码',
  `region_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区域名称',
  `salary_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '薪资类型（hourly/daily/monthly/piece）',
  `salary_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '最低薪资',
  `salary_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '最高薪资',
  `work_hours_per_day` int(0) NULL DEFAULT NULL COMMENT '每日工作小时数',
  `work_days_per_week` int(0) NULL DEFAULT NULL COMMENT '每周工作天数',
  `start_date` date NULL DEFAULT NULL COMMENT '开始日期',
  `end_date` date NULL DEFAULT NULL COMMENT '结束日期',
  `positions_needed` int(0) NULL DEFAULT 1 COMMENT '需要人数',
  `positions_filled` int(0) NULL DEFAULT 0 COMMENT '已招聘人数',
  `education_required` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学历要求',
  `experience_required` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '经验要求',
  `age_min` int(0) NULL DEFAULT NULL COMMENT '最小年龄要求',
  `age_max` int(0) NULL DEFAULT NULL COMMENT '最大年龄要求',
  `gender_required` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '性别要求（male/female/unlimited）',
  `skills_required` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '技能要求（JSON格式存储）',
  `work_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '工作描述',
  `welfare_benefits` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '福利待遇',
  `contact_person` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '公司名称',
  `company_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '公司地址',
  `company_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '公司描述',
  `urgency_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'normal' COMMENT '紧急程度（urgent/high/normal/low）',
  `application_deadline` datetime(0) NULL DEFAULT NULL COMMENT '申请截止时间',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'draft' COMMENT '状态（draft/published/paused/closed/completed）',
  `is_verified` tinyint(1) NULL DEFAULT 0 COMMENT '是否已验证（0否 1是）',
  `is_featured` tinyint(1) NULL DEFAULT 0 COMMENT '是否推荐（0否 1是）',
  `view_count` int(0) NULL DEFAULT 0 COMMENT '浏览次数',
  `application_count` int(0) NULL DEFAULT 0 COMMENT '申请次数',
  `publisher_user_id` bigint(0) NOT NULL COMMENT '发布者用户ID',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`employment_id`) USING BTREE,
  INDEX `idx_employment_type`(`employment_type`) USING BTREE,
  INDEX `idx_work_category`(`work_category`) USING BTREE,
  INDEX `idx_salary_type`(`salary_type`) USING BTREE,
  INDEX `idx_region_code`(`region_code`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_is_featured`(`is_featured`) USING BTREE,
  INDEX `idx_publisher_user_id`(`publisher_user_id`) USING BTREE,
  INDEX `idx_del_flag`(`del_flag`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用工信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of employment_info
-- ----------------------------
INSERT INTO `employment_info` VALUES (1, '餐厅服务员招聘', '日结', '服务员', '青岛市市南区香港中路', '370202', '市南区', 'daily', 120.00, 150.00, 8, 6, '2025-07-25', '2025-12-31', 5, 0, '高中', '有餐厅服务经验优先', 18, 45, 'unlimited', '[\"餐厅服务\", \"客户接待\", \"收银操作\", \"礼仪服务\"]', '负责餐厅日常服务工作，包括点餐、上菜、收银等，要求服务态度好，沟通能力强', '包工作餐，提供制服，表现优秀者可转正', '张经理', '13800138001', '<EMAIL>', '海景餐厅', '青岛市市南区香港中路88号', '知名海鲜餐厅，环境优雅，客流量大', 'normal', '2025-08-15 18:00:00', 'published', 1, 1, 0, 0, 1001, 1, '2025-07-23 23:46:56', NULL, '2025-07-23 23:46:56', '0', NULL);
INSERT INTO `employment_info` VALUES (2, '保洁员长期招聘', '月结', '保洁', '青岛市崂山区科技路', '370212', '崂山区', 'monthly', 3500.00, 4200.00, 8, 6, '2025-08-01', '2025-12-31', 3, 0, '不限', '有保洁工作经验', 25, 55, 'female', '[\"清洁技能\", \"使用清洁设备\", \"垃圾分类\"]', '负责办公楼保洁工作，包括地面清洁、垃圾清理、卫生间清洁等', '五险一金，带薪年假，节日福利', '李主管', '13900139001', '<EMAIL>', '青岛清洁服务公司', '青岛市崂山区科技路200号', '专业清洁服务公司，服务品质优良', 'normal', '2025-08-20 17:00:00', 'published', 1, 0, 0, 0, 1002, 1, '2025-07-23 23:46:56', NULL, '2025-07-23 23:46:56', '0', NULL);
INSERT INTO `employment_info` VALUES (3, '搬运工临时招聘', '日结', '搬运工', '青岛市市北区重庆南路', '370203', '市北区', 'daily', 200.00, 250.00, 10, 7, '2025-07-24', '2025-08-10', 10, 0, '不限', '身体健康，能吃苦耐劳', 20, 50, 'male', '[\"体力劳动\", \"货物搬运\", \"团队协作\"]', '负责货物搬运工作，要求身体健康，能够承受重体力劳动', '按日结算，多劳多得，提供工作餐', '王总', '13700137001', '<EMAIL>', '青岛物流公司', '青岛市市北区重庆南路150号', '大型物流企业，业务量稳定', 'urgent', '2025-07-25 12:00:00', 'published', 1, 1, 0, 0, 1003, 1, '2025-07-23 23:46:56', 1, '2025-07-24 00:00:34', '0', NULL);
INSERT INTO `employment_info` VALUES (4, '销售助理兼职', '周结', '销售', '青岛市市南区中山路', '370202', '市南区', 'weekly', 800.00, 1200.00, 6, 5, '2025-08-01', '2025-11-30', 2, 0, '大专', '有销售经验或相关专业', 22, 35, 'unlimited', '[\"销售技巧\", \"客户沟通\", \"产品介绍\", \"办公软件\"]', '协助销售经理开展销售工作，包括客户接待、产品介绍、订单处理等', '提成奖励，培训机会，弹性工作时间', '陈经理', '13600136001', '<EMAIL>', '青岛贸易公司', '青岛市市南区中山路66号', '专业贸易公司，产品质量优良', 'normal', '2025-08-10 18:00:00', 'published', 1, 0, 0, 0, 1004, 1, '2025-07-23 23:46:56', NULL, '2025-07-23 23:46:56', '0', NULL);
INSERT INTO `employment_info` VALUES (5, '厨师助手急招', '日结', '厨师助手', '青岛市崂山区海尔路', '370212', '崂山区', 'daily', 150.00, 180.00, 10, 6, '2025-07-24', '2025-09-30', 3, 0, '中专', '有厨房工作经验', 20, 40, 'male', '[\"食材准备\", \"菜品制作\", \"厨房清洁\", \"中式烹饪\"]', '协助主厨进行食材准备、菜品制作等工作，要求刀工娴熟，工作效率高', '包食宿，提供工作服，技能培训', '马师傅', '13500135001', '<EMAIL>', '海鲜大酒店', '青岛市崂山区海尔路300号', '五星级酒店，厨房设备先进', 'high', '2025-07-26 20:00:00', 'published', 1, 1, 0, 0, 1005, 1, '2025-07-23 23:46:56', NULL, '2025-07-23 23:46:56', '0', NULL);

-- ----------------------------
-- Table structure for job_application
-- ----------------------------
DROP TABLE IF EXISTS `job_application`;
CREATE TABLE `job_application`  (
  `application_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `job_id` bigint(0) NOT NULL COMMENT '招聘ID',
  `worker_id` bigint(0) NOT NULL COMMENT '零工ID',
  `application_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'pending' COMMENT '申请状态（pending/accepted/rejected/withdrawn/completed）',
  `application_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '申请留言',
  `employer_response` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '雇主回复',
  `interview_time` datetime(0) NULL DEFAULT NULL COMMENT '面试时间',
  `interview_location` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '面试地点',
  `interview_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '面试备注',
  `start_work_time` datetime(0) NULL DEFAULT NULL COMMENT '开始工作时间',
  `end_work_time` datetime(0) NULL DEFAULT NULL COMMENT '结束工作时间',
  `actual_salary` decimal(10, 2) NULL DEFAULT NULL COMMENT '实际薪资',
  `work_rating` decimal(3, 2) NULL DEFAULT NULL COMMENT '工作评分',
  `work_feedback` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '工作反馈',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`application_id`) USING BTREE,
  UNIQUE INDEX `uk_job_worker`(`job_id`, `worker_id`) USING BTREE,
  INDEX `idx_job_id`(`job_id`) USING BTREE,
  INDEX `idx_worker_id`(`worker_id`) USING BTREE,
  INDEX `idx_status`(`application_status`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_del_flag`(`del_flag`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '工作申请表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of job_application
-- ----------------------------

-- ----------------------------
-- Table structure for job_match_record
-- ----------------------------
DROP TABLE IF EXISTS `job_match_record`;
CREATE TABLE `job_match_record`  (
  `match_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '匹配ID',
  `job_id` bigint(0) NOT NULL COMMENT '招聘ID',
  `worker_id` bigint(0) NOT NULL COMMENT '零工ID',
  `match_score` decimal(5, 2) NOT NULL COMMENT '匹配分数（0-100）',
  `match_factors` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '匹配因素详情（JSON格式）',
  `match_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'system' COMMENT '匹配类型（system/manual）',
  `is_viewed_by_employer` tinyint(1) NULL DEFAULT 0 COMMENT '雇主是否已查看（0否 1是）',
  `is_viewed_by_worker` tinyint(1) NULL DEFAULT 0 COMMENT '零工是否已查看（0否 1是）',
  `employer_interest` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '雇主兴趣（interested/not_interested/contacted）',
  `worker_interest` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '零工兴趣（interested/not_interested/applied）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`match_id`) USING BTREE,
  UNIQUE INDEX `uk_job_worker_match`(`job_id`, `worker_id`) USING BTREE,
  INDEX `idx_job_id`(`job_id`) USING BTREE,
  INDEX `idx_worker_id`(`worker_id`) USING BTREE,
  INDEX `idx_match_score`(`match_score`) USING BTREE,
  INDEX `idx_match_type`(`match_type`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_del_flag`(`del_flag`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '工作匹配记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of job_match_record
-- ----------------------------

-- ----------------------------
-- Table structure for job_posting
-- ----------------------------
DROP TABLE IF EXISTS `job_posting`;
CREATE TABLE `job_posting`  (
  `job_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '招聘ID',
  `job_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '职位名称',
  `job_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '职位描述',
  `job_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工作类型（全职/兼职/临时工/小时工）',
  `job_category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工作类别（服务员/保洁/搬运工/销售/厨师助手/快递员/保安等）',
  `work_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工作地点（保留但不作为主要匹配条件）',
  `salary_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '薪资类型（hourly/daily/monthly/piece）',
  `salary_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '最低薪资',
  `salary_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '最高薪资',
  `education_required` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学历要求（不限/初中/高中/中专/大专/本科/硕士/博士）',
  `experience_required` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '经验要求',
  `work_hours_per_day` int(0) NULL DEFAULT NULL COMMENT '每日工作小时数',
  `work_days_per_week` int(0) NULL DEFAULT NULL COMMENT '每周工作天数',
  `start_date` date NULL DEFAULT NULL COMMENT '开始日期',
  `end_date` date NULL DEFAULT NULL COMMENT '结束日期',
  `contact_person` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '公司名称',
  `urgency_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'normal' COMMENT '紧急程度（urgent/high/normal/low）',
  `positions_available` int(0) NULL DEFAULT 1 COMMENT '招聘人数',
  `positions_filled` int(0) NULL DEFAULT 0 COMMENT '已招聘人数',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'draft' COMMENT '状态（draft/published/paused/closed/completed）',
  `view_count` int(0) NULL DEFAULT 0 COMMENT '浏览次数',
  `application_count` int(0) NULL DEFAULT 0 COMMENT '申请次数',
  `publisher_user_id` bigint(0) NOT NULL COMMENT '发布者用户ID',
  `is_verified` tinyint(1) NULL DEFAULT 0 COMMENT '是否已验证（0否 1是）',
  `featured` tinyint(1) NULL DEFAULT 0 COMMENT '是否推荐（0否 1是）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`job_id`) USING BTREE,
  INDEX `idx_job_type`(`job_type`) USING BTREE,
  INDEX `idx_job_category`(`job_category`) USING BTREE,
  INDEX `idx_salary_type`(`salary_type`) USING BTREE,
  INDEX `idx_education`(`education_required`) USING BTREE,
  INDEX `idx_salary_range`(`salary_min`, `salary_max`) USING BTREE,
  INDEX `idx_match_core`(`job_type`, `job_category`, `salary_type`, `status`) USING BTREE,
  INDEX `idx_match_salary`(`salary_type`, `salary_min`, `salary_max`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_publisher`(`publisher_user_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_del_flag`(`del_flag`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '招聘信息表（核心匹配优化版）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of job_posting
-- ----------------------------
INSERT INTO `job_posting` VALUES (1, '星级酒店服务员', '五星级酒店餐厅服务员，负责客人接待、点餐服务、餐具整理等工作，要求形象良好，服务意识强', '兼职', '服务员', '青岛市市南区', 'hourly', 25.00, 35.00, '高中', '1年以上服务经验', 8, 5, '2025-08-01', '2025-12-31', '王经理', '13800138001', '青岛国际大酒店', 'high', 5, 0, 'published', 159, 23, 1, 1, 1, NULL, '2025-07-23 22:03:10', NULL, '2025-07-24 00:05:47', '0', NULL);
INSERT INTO `job_posting` VALUES (2, '快餐店服务员', '连锁快餐店服务员，负责点餐、收银、清洁等工作，工作轻松，适合学生兼职', '兼职', '服务员', '青岛市李沧区', 'hourly', 18.00, 22.00, '不限', '无经验要求', 6, 6, '2025-07-25', '2025-10-31', '李店长', '13800138002', '麦当劳青岛店', 'normal', 8, 2, 'published', 89, 15, 1, 1, 0, NULL, '2025-07-23 22:03:10', NULL, '2025-07-23 22:03:10', '0', NULL);
INSERT INTO `job_posting` VALUES (3, '咖啡厅服务员', '精品咖啡厅服务员，负责咖啡制作、客户服务，要求有一定的咖啡知识和英语基础', '全职', '服务员', '青岛市崂山区', 'monthly', 3500.00, 4500.00, '大专', '咖啡制作经验优先', 8, 6, '2025-08-01', NULL, '张经理', '13800138003', '星巴克青岛店', 'normal', 3, 1, 'published', 254, 45, 1, 1, 1, NULL, '2025-07-23 22:03:10', NULL, '2025-07-23 22:26:58', '0', NULL);
INSERT INTO `job_posting` VALUES (4, '办公楼保洁员', '负责办公楼日常清洁工作，包括地面清洁、垃圾清理、卫生间清洁等', '临时工', '保洁', '青岛市市北区', 'daily', 120.00, 150.00, '不限', '无经验要求', 8, 6, '2025-07-25', '2025-09-30', '陈主管', '13800138004', '青岛物业管理公司', 'urgent', 10, 3, 'published', 67, 8, 1, 1, 0, NULL, '2025-07-23 22:03:10', NULL, '2025-07-23 22:03:10', '0', NULL);
INSERT INTO `job_posting` VALUES (5, '家庭保洁员', '上门家庭保洁服务，包括房屋清洁、整理收纳等，时间灵活，按次计费', '小时工', '保洁', '青岛市城阳区', 'hourly', 30.00, 40.00, '不限', '有家政经验优先', 4, 7, '2025-07-25', NULL, '刘经理', '13800138005', '青岛家政服务中心', 'normal', 20, 5, 'published', 145, 32, 1, 1, 0, NULL, '2025-07-23 22:03:10', NULL, '2025-07-23 22:03:10', '0', NULL);
INSERT INTO `job_posting` VALUES (6, '仓库搬运工', '负责仓库货物搬运、装卸、整理等工作，要求身体健康，能吃苦耐劳', '临时工', '搬运工', '青岛市黄岛区', 'daily', 180.00, 220.00, '不限', '无经验要求', 8, 6, '2025-07-25', '2025-08-31', '赵主管', '13800138006', '青岛物流有限公司', 'high', 15, 8, 'published', 198, 67, 1, 1, 0, NULL, '2025-07-23 22:03:10', NULL, '2025-07-23 22:03:10', '0', NULL);
INSERT INTO `job_posting` VALUES (7, '搬家搬运工', '专业搬家公司搬运工，负责家具、电器等物品的搬运和安装', '兼职', '搬运工', '青岛市即墨区', 'hourly', 25.00, 35.00, '不限', '有搬运经验优先', 8, 7, '2025-07-25', NULL, '孙经理', '13800138007', '青岛搬家公司', 'normal', 12, 4, 'published', 123, 28, 1, 1, 0, NULL, '2025-07-23 22:03:10', NULL, '2025-07-23 22:03:10', '0', NULL);
INSERT INTO `job_posting` VALUES (8, '房产销售顾问', '负责房产销售，客户接待，合同签订等工作，有提成，收入可观', '全职', '销售', '青岛市市南区', 'monthly', 4000.00, 8000.00, '大专', '1-3年销售经验', 8, 6, '2025-08-01', NULL, '周经理', '13800138008', '青岛房地产公司', 'normal', 5, 2, 'published', 267, 89, 1, 1, 1, NULL, '2025-07-23 22:03:10', NULL, '2025-07-23 22:03:10', '0', NULL);
INSERT INTO `job_posting` VALUES (9, '超市促销员', '负责超市商品促销，向顾客介绍产品，协助销售', '兼职', '销售', '青岛市李沧区', 'hourly', 20.00, 25.00, '高中', '有销售经验优先', 6, 6, '2025-07-25', '2025-09-30', '吴主管', '13800138009', '家乐福青岛店', 'normal', 6, 1, 'published', 156, 34, 1, 1, 0, NULL, '2025-07-23 22:03:10', NULL, '2025-07-23 22:03:10', '0', NULL);
INSERT INTO `job_posting` VALUES (10, '中餐厅厨师助手', '协助主厨进行菜品制作，负责食材准备、清洗、切配等工作', '全职', '厨师助手', '青岛市市北区', 'monthly', 3000.00, 4000.00, '不限', '有厨房工作经验优先', 10, 6, '2025-08-01', NULL, '马师傅', '13800138010', '青岛海鲜酒楼', 'normal', 4, 1, 'published', 90, 12, 1, 1, 0, NULL, '2025-07-23 22:03:10', NULL, '2025-07-23 22:18:48', '0', NULL);
INSERT INTO `job_posting` VALUES (11, '快递配送员', '负责快递包裹的配送工作，要求有电动车或摩托车驾驶证', '全职', '快递员', '青岛市城阳区', 'monthly', 4500.00, 6000.00, '不限', '有配送经验优先', 10, 6, '2025-07-25', NULL, '郑站长', '13800138011', '顺丰快递青岛站', 'urgent', 8, 3, 'published', 345, 78, 1, 1, 0, NULL, '2025-07-23 22:03:10', NULL, '2025-07-23 22:03:10', '0', NULL);
INSERT INTO `job_posting` VALUES (12, '小区保安', '负责小区安全巡逻、门岗值守、车辆管理等工作', '全职', '保安', '青岛市崂山区', 'monthly', 3200.00, 3800.00, '不限', '有保安证优先', 12, 6, '2025-08-01', NULL, '田队长', '13800138012', '青岛物业管理公司', 'normal', 6, 2, 'published', 178, 23, 1, 1, 0, NULL, '2025-07-23 22:03:10', NULL, '2025-07-23 22:03:10', '0', NULL);
INSERT INTO `job_posting` VALUES (13, '超市收银员', '负责商品收银、会员服务、商品整理等工作', '兼职', '收银员', '青岛市黄岛区', 'hourly', 18.00, 22.00, '高中', '有收银经验优先', 6, 6, '2025-07-25', '2025-10-31', '何经理', '13800138013', '大润发青岛店', 'normal', 10, 4, 'published', 234, 56, 1, 1, 0, NULL, '2025-07-23 22:03:10', NULL, '2025-07-23 22:03:10', '0', NULL);
INSERT INTO `job_posting` VALUES (14, '超市', '超市', '全职', '销售', '北京', 'monthly', 10000.00, 100000.00, '初中', NULL, NULL, NULL, NULL, NULL, '14', '14456767676', NULL, 'normal', 10, 0, 'draft', 2, 0, 1, 0, 0, 1, '2025-07-23 22:22:06', 1, '2025-07-23 22:22:14', '0', NULL);

-- ----------------------------
-- Table structure for labor_market_info
-- ----------------------------
DROP TABLE IF EXISTS `labor_market_info`;
CREATE TABLE `labor_market_info`  (
  `market_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '市场ID',
  `market_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '市场名称',
  `market_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '市场编码',
  `market_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '市场类型（综合市场/专业市场/临时市场）',
  `address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '市场地址',
  `region_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区域代码',
  `region_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区域名称',
  `longitude` decimal(10, 6) NULL DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10, 6) NULL DEFAULT NULL COMMENT '纬度',
  `contact_person` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `operating_hours` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '营业时间',
  `service_categories` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '服务类别（JSON格式存储）',
  `worker_capacity` int(0) NULL DEFAULT 0 COMMENT '零工容纳量',
  `current_worker_count` int(0) NULL DEFAULT 0 COMMENT '当前零工数量',
  `daily_avg_demand` int(0) NULL DEFAULT 0 COMMENT '日均用工需求',
  `peak_demand_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用工高峰时段',
  `management_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT '管理费用（元/人/天）',
  `service_fee_rate` decimal(5, 2) NULL DEFAULT NULL COMMENT '服务费率（%）',
  `facilities` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '配套设施（JSON格式存储）',
  `safety_measures` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '安全措施描述',
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '市场主图片URL',
  `image_gallery` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '市场图片集（JSON格式存储多张图片URL）',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '市场详细描述',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `is_featured` tinyint(1) NULL DEFAULT 0 COMMENT '是否推荐（0否 1是）',
  `sort_order` int(0) NULL DEFAULT 0 COMMENT '排序号',
  `view_count` int(0) NULL DEFAULT 0 COMMENT '浏览次数',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`market_id`) USING BTREE,
  INDEX `idx_market_type`(`market_type`) USING BTREE,
  INDEX `idx_region_code`(`region_code`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_is_featured`(`is_featured`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_del_flag`(`del_flag`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '零工市场基础信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of labor_market_info
-- ----------------------------
INSERT INTO `labor_market_info` VALUES (1, '青岛市中心零工市场', 'QDZXLG001', '综合市场', '青岛市市南区中山路88号', '370202', '市南区', NULL, NULL, '赵经理', '0532-55555555', '<EMAIL>', '06:00-20:00', '[\"服务员\", \"保洁\", \"搬运工\", \"销售\", \"厨师助手\", \"快递员\", \"保安\", \"临时工\"]', 500, 320, 180, '08:00-10:00,14:00-16:00', 10.00, 5.00, '[\"休息区\", \"饮水设施\", \"卫生间\", \"信息发布栏\", \"监控系统\", \"医务室\"]', '实名制管理，24小时监控，定期安全培训，购买意外保险', './image/market1.jpg', NULL, '青岛市最大的综合性零工市场，服务类别齐全，管理规范', '0', 1, 1, 0, 1, '2025-07-23 23:46:56', NULL, '2025-07-23 23:46:56', '0', NULL);
INSERT INTO `labor_market_info` VALUES (2, '崂山区专业零工市场', 'LSZYLG002', '专业市场', '青岛市崂山区海尔路300号', '370212', '崂山区', NULL, NULL, '孙主管', '0532-44444444', '<EMAIL>', '07:00-19:00', '[\"技术工人\", \"装修工\", \"电工\", \"水暖工\", \"园艺工\", \"维修工\"]', 200, 150, 80, '09:00-11:00,15:00-17:00', 15.00, 8.00, '[\"技能培训室\", \"工具存放区\", \"安全防护用品\", \"休息室\"]', '持证上岗，技能认证，安全操作培训，工伤保险覆盖', './image/market2.jpg', NULL, '专注于技术型零工服务，工人技能水平较高', '0', 1, 2, 0, 1, '2025-07-23 23:46:56', NULL, '2025-07-23 23:46:56', '0', NULL);
INSERT INTO `labor_market_info` VALUES (3, '市北区临时零工市场', 'SBLSLG003', '临时市场', '青岛市市北区台东路120号', '370203', '市北区', NULL, NULL, '钱站长', '0532-33333333', '<EMAIL>', '05:30-18:00', '[\"日结工\", \"小时工\", \"临时搬运\", \"活动服务\", \"清洁工\"]', 300, 200, 120, '06:00-08:00,17:00-18:00', 8.00, 3.00, '[\"候工区\", \"信息公告栏\", \"简易休息设施\", \"饮水点\"]', '身份验证，现场管理，基础安全提醒', './image/market3.jpg', NULL, '主要服务临时性用工需求，灵活便民', '0', 0, 3, 0, 1, '2025-07-23 23:46:56', NULL, '2025-07-23 23:46:56', '0', NULL);

-- ----------------------------
-- Table structure for match_record
-- ----------------------------
DROP TABLE IF EXISTS `match_record`;
CREATE TABLE `match_record`  (
  `match_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '匹配ID',
  `recruitment_id` bigint(0) NOT NULL COMMENT '招聘ID',
  `worker_id` bigint(0) NOT NULL COMMENT '零工ID',
  `match_score` decimal(5, 2) NULL DEFAULT NULL COMMENT '匹配分数(0-100)',
  `skill_match_score` decimal(5, 2) NULL DEFAULT NULL COMMENT '技能匹配分数',
  `location_match_score` decimal(5, 2) NULL DEFAULT NULL COMMENT '地点匹配分数',
  `salary_match_score` decimal(5, 2) NULL DEFAULT NULL COMMENT '薪资匹配分数',
  `experience_match_score` decimal(5, 2) NULL DEFAULT NULL COMMENT '经验匹配分数',
  `education_match_score` decimal(5, 2) NULL DEFAULT NULL COMMENT '学历匹配分数',
  `time_match_score` decimal(5, 2) NULL DEFAULT NULL COMMENT '时间匹配分数',
  `match_details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '匹配详情(JSON格式)',
  `match_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '匹配状态(0待处理 1已推荐 2已申请 3已拒绝)',
  `is_mutual_match` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '是否双向匹配(0否 1是)',
  `employer_viewed` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '雇主是否已查看(0否 1是)',
  `worker_viewed` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '零工是否已查看(0否 1是)',
  `match_time` datetime(0) NULL DEFAULT NULL COMMENT '匹配时间',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`match_id`) USING BTREE,
  UNIQUE INDEX `uk_recruitment_worker`(`recruitment_id`, `worker_id`) USING BTREE,
  INDEX `idx_recruitment_id`(`recruitment_id`) USING BTREE,
  INDEX `idx_worker_id`(`worker_id`) USING BTREE,
  INDEX `idx_match_score`(`match_score`) USING BTREE,
  INDEX `idx_match_status`(`match_status`) USING BTREE,
  INDEX `idx_match_time`(`match_time`) USING BTREE,
  CONSTRAINT `fk_match_record_recruitment` FOREIGN KEY (`recruitment_id`) REFERENCES `recruitment_info` (`recruitment_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_match_record_worker` FOREIGN KEY (`worker_id`) REFERENCES `worker_info` (`worker_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '匹配记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of match_record
-- ----------------------------

-- ----------------------------
-- Table structure for place_info
-- ----------------------------
DROP TABLE IF EXISTS `place_info`;
CREATE TABLE `place_info`  (
  `place_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '场地ID',
  `place_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '场地名称',
  `place_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '场地编码',
  `place_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '场地类型（创业园区/孵化器/众创空间/产业园等）',
  `place_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '场地等级（国家级/省级/市级/区级）',
  `place_area` decimal(10, 2) NULL DEFAULT NULL COMMENT '场地面积（平方米）',
  `usable_area` decimal(10, 2) NULL DEFAULT NULL COMMENT '可使用面积（平方米）',
  `address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '详细地址',
  `region_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区域代码',
  `region_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区域名称',
  `longitude` decimal(10, 6) NULL DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10, 6) NULL DEFAULT NULL COMMENT '纬度',
  `contact_person` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `company_count` int(0) NULL DEFAULT 0 COMMENT '已入驻企业数量',
  `available_positions` int(0) NULL DEFAULT 0 COMMENT '可提供工位数',
  `occupied_positions` int(0) NULL DEFAULT 0 COMMENT '已占用工位数',
  `rent_price_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '最低租金（元/月/平方米）',
  `rent_price_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '最高租金（元/月/平方米）',
  `operation_mode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '运营模式（自营/委托运营/合作运营）',
  `industry_direction` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '行业方向（多个用逗号分隔）',
  `service_facilities` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '服务设施（JSON格式存储）',
  `preferential_policies` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '优惠政策描述',
  `apply_start_date` date NULL DEFAULT NULL COMMENT '招商开始时间',
  `apply_end_date` date NULL DEFAULT NULL COMMENT '招商结束时间',
  `apply_time_status` tinyint(1) NULL DEFAULT 0 COMMENT '招商时间状态（0长期 1定期）',
  `is_open_settle` tinyint(1) NULL DEFAULT 1 COMMENT '是否开放入驻（0否 1是）',
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '场地主图片URL',
  `image_gallery` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '场地图片集（JSON格式存储多张图片URL）',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '场地详细描述',
  `notice_detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '场地公告详情',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `is_featured` tinyint(1) NULL DEFAULT 0 COMMENT '是否推荐（0否 1是）',
  `sort_order` int(0) NULL DEFAULT 0 COMMENT '排序号',
  `view_count` int(0) NULL DEFAULT 0 COMMENT '浏览次数',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`place_id`) USING BTREE,
  INDEX `idx_place_type`(`place_type`) USING BTREE,
  INDEX `idx_place_level`(`place_level`) USING BTREE,
  INDEX `idx_region_code`(`region_code`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_is_featured`(`is_featured`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_del_flag`(`del_flag`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '场地信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of place_info
-- ----------------------------
INSERT INTO `place_info` VALUES (1, '青岛国际创新园', 'QDICX001', '创业园区', '国家级', 50000.00, 35000.00, '青岛市市南区香港中路100号', '370202', '市南区', NULL, NULL, '张经理', '0532-88888888', '<EMAIL>', 85, 200, 170, 80.00, 120.00, '自营', '互联网,人工智能,生物医药', '[\"会议室\", \"路演厅\", \"咖啡厅\", \"健身房\", \"停车场\", \"24小时安保\"]', '前三年租金减免50%，提供创业导师服务，优先推荐投资机构', '2025-01-01', '2025-12-31', 0, 1, './image/place1.jpg', NULL, '青岛市重点打造的国际化创新创业园区，配套设施完善，政策支持力度大', '欢迎各类创新创业企业入驻，共同打造创新生态圈', '0', 1, 1, 0, 1, '2025-07-23 23:46:56', NULL, '2025-07-23 23:46:56', '0', NULL);
INSERT INTO `place_info` VALUES (2, '崂山科技孵化器', 'LSKJFH002', '孵化器', '省级', 30000.00, 22000.00, '青岛市崂山区科技路200号', '370212', '崂山区', NULL, NULL, '李主任', '0532-77777777', '<EMAIL>', 45, 120, 95, 60.00, 90.00, '委托运营', '软件开发,电子商务,新材料', '[\"创客空间\", \"实验室\", \"产品展示厅\", \"商务中心\", \"餐厅\"]', '提供免费办公场地6个月，专业孵化服务，投融资对接', '2025-03-01', '2025-11-30', 1, 1, './image/place2.jpg', NULL, '专注于科技型企业孵化，拥有完善的孵化服务体系', '现面向全市招募优秀科技创业项目', '0', 1, 2, 0, 1, '2025-07-23 23:46:56', NULL, '2025-07-23 23:46:56', '0', NULL);
INSERT INTO `place_info` VALUES (3, '市北众创空间', 'SBZCKG003', '众创空间', '市级', 15000.00, 12000.00, '青岛市市北区重庆南路150号', '370203', '市北区', NULL, NULL, '王总', '0532-66666666', '<EMAIL>', 28, 80, 65, 40.00, 70.00, '合作运营', '文化创意,设计服务,教育培训', '[\"开放办公区\", \"独立工位\", \"会议室\", \"打印复印\", \"茶水间\"]', '工位租金优惠，提供创业培训，定期举办创业沙龙', '2025-02-01', '2025-10-31', 1, 1, './image/place3.jpg', NULL, '面向初创企业和个人创业者的低成本创业空间', '为创业者提供便利的办公环境和创业服务', '0', 0, 3, 0, 1, '2025-07-23 23:46:56', NULL, '2025-07-23 23:46:56', '0', NULL);

-- ----------------------------
-- Table structure for policy_application
-- ----------------------------
DROP TABLE IF EXISTS `policy_application`;
CREATE TABLE `policy_application`  (
  `application_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `policy_id` bigint(0) NOT NULL COMMENT '政策ID',
  `applicant_user_id` bigint(0) NOT NULL COMMENT '申请人用户ID',
  `applicant_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '申请人姓名',
  `applicant_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '申请人手机号',
  `application_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '申请状态（0待初审 1初审通过 2初审拒绝 3待终审 4终审通过 5终审拒绝 6已完成）',
  `required_materials` json NULL COMMENT '所需材料JSON数据',
  `submit_time` datetime(0) NULL DEFAULT NULL COMMENT '提交时间',
  `complete_time` datetime(0) NULL DEFAULT NULL COMMENT '完成时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`application_id`) USING BTREE,
  INDEX `idx_policy_id`(`policy_id`) USING BTREE,
  INDEX `idx_applicant_user_id`(`applicant_user_id`) USING BTREE,
  INDEX `idx_application_status`(`application_status`) USING BTREE,
  INDEX `idx_submit_time`(`submit_time`) USING BTREE,
  INDEX `idx_applicant_name`(`applicant_name`) USING BTREE,
  INDEX `idx_applicant_phone`(`applicant_phone`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '政策申请表（包含申请人基本信息）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of policy_application
-- ----------------------------
INSERT INTO `policy_application` VALUES (1, 1, 2, NULL, NULL, '5', '[{\"name\": \"《企业新增岗位吸纳就业困难人员、高校毕业生和退役军人社保补贴申领表》\", \"type\": \"form\", \"required\": true, \"uploaded\": false, \"file_path\": \"\"}, {\"name\": \"企业的营业执照副本复印件\", \"type\": \"license\", \"required\": true, \"uploaded\": false, \"file_path\": \"\"}, {\"name\": \"退役军人需提供退伍证原件及复印件\", \"type\": \"certificate\", \"required\": false, \"uploaded\": false, \"file_path\": \"\"}, {\"name\": \"企业社保缴费凭证\", \"type\": \"proof\", \"required\": true, \"uploaded\": false, \"file_path\": \"\"}, {\"name\": \"企业在银行开立的基本账户证明\", \"type\": \"account\", \"required\": true, \"uploaded\": false, \"file_path\": \"\"}]', '2025-07-21 00:00:00', NULL, '0', 2, '2025-07-21 00:00:00', NULL, NULL, '示例政策申请');
INSERT INTO `policy_application` VALUES (2, 1, 102, '张三', '***********', '0', '[{\"name\": \"《企业新增岗位吸纳就业困难人员、高校毕业生和退役军人社保补贴申领表》\", \"files\": [{\"uid\": *************, \"name\": \"【哲风壁纸】二次元场景-动漫插画_20250723000455A001.png\", \"status\": \"success\", \"filePath\": \"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】二次元场景-动漫插画_20250723000455A001.png\", \"sourceFileName\": \"【哲风壁纸】二次元场景-动漫插画.png\"}], \"required\": true}, {\"name\": \"企业的营业执照副本复印件\", \"files\": [{\"uid\": *************, \"name\": \"【哲风壁纸】可爱-可爱猫-大眼猫_20250723000457A002.png\", \"status\": \"success\", \"filePath\": \"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723000457A002.png\", \"sourceFileName\": \"【哲风壁纸】可爱-可爱猫-大眼猫.png\"}], \"required\": true}, {\"name\": \"退役军人需提供退伍证原件及复印件\", \"files\": [{\"uid\": 1753200299883, \"name\": \"【哲风壁纸】可爱-可爱猫-大眼猫_20250723000459A003.png\", \"status\": \"success\", \"filePath\": \"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723000459A003.png\", \"sourceFileName\": \"【哲风壁纸】可爱-可爱猫-大眼猫.png\"}], \"required\": false}, {\"name\": \"企业社保缴费凭证\", \"files\": [{\"uid\": 1753200302121, \"name\": \"【哲风壁纸】可爱-可爱猫-大眼猫_20250723000502A004.png\", \"status\": \"success\", \"filePath\": \"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723000502A004.png\", \"sourceFileName\": \"【哲风壁纸】可爱-可爱猫-大眼猫.png\"}], \"required\": true}, {\"name\": \"企业在银行开立的基本账户\", \"files\": [{\"uid\": 1753200303955, \"name\": \"【哲风壁纸】可爱-可爱猫-大眼猫_20250723000503A005.png\", \"status\": \"success\", \"filePath\": \"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723000503A005.png\", \"sourceFileName\": \"【哲风壁纸】可爱-可爱猫-大眼猫.png\"}], \"required\": true}]', '2025-07-23 00:05:15', NULL, '0', 102, '2025-07-23 00:05:15', NULL, NULL, '');

-- ----------------------------
-- Table structure for policy_approval_record
-- ----------------------------
DROP TABLE IF EXISTS `policy_approval_record`;
CREATE TABLE `policy_approval_record`  (
  `record_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '审批记录ID',
  `application_id` bigint(0) NOT NULL COMMENT '申请ID',
  `approval_level` tinyint(0) NOT NULL COMMENT '审批层级（1初审 2终审）',
  `approval_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '审批状态（0待审批 1审批通过 2审批拒绝）',
  `approver_user_id` bigint(0) NULL DEFAULT NULL COMMENT '审批人用户ID',
  `approval_time` datetime(0) NULL DEFAULT NULL COMMENT '审批时间',
  `approval_comment` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审批意见',
  `approval_files` json NULL COMMENT '审批相关文件JSON数据',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`record_id`) USING BTREE,
  INDEX `idx_application_id`(`application_id`) USING BTREE,
  INDEX `idx_approval_level`(`approval_level`) USING BTREE,
  INDEX `idx_approval_status`(`approval_status`) USING BTREE,
  INDEX `idx_approver_user_id`(`approver_user_id`) USING BTREE,
  INDEX `idx_approval_time`(`approval_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '政策审批记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of policy_approval_record
-- ----------------------------
INSERT INTO `policy_approval_record` VALUES (1, 1, 1, '1', 100, '2025-07-23 00:08:53', 'ces ', NULL, '0', 1, '2025-07-21 00:00:00', NULL, NULL, '初审记录');
INSERT INTO `policy_approval_record` VALUES (2, 1, 2, '2', 101, '2025-07-23 00:09:25', 'ceshi ', NULL, '0', 1, '2025-07-21 00:00:00', NULL, NULL, '终审记录');
INSERT INTO `policy_approval_record` VALUES (3, 2, 1, '0', NULL, NULL, NULL, NULL, '0', 102, '2025-07-23 00:05:15', NULL, NULL, NULL);
INSERT INTO `policy_approval_record` VALUES (4, 2, 2, '0', NULL, NULL, NULL, NULL, '0', 102, '2025-07-23 00:05:15', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for policy_info
-- ----------------------------
DROP TABLE IF EXISTS `policy_info`;
CREATE TABLE `policy_info`  (
  `policy_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '政策ID',
  `policy_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '政策名称',
  `policy_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '政策描述',
  `policy_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '政策类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`policy_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '政策信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of policy_info
-- ----------------------------
INSERT INTO `policy_info` VALUES (1, '企业新增岗位吸纳就业困难人员、高校毕业生和退役军人社保补贴', '为鼓励企业吸纳就业困难人员、高校毕业生和退役军人就业，给予企业相应的社会保险补贴。申请企业需要提供相关证明材料，经过初审和终审两级审批后，方可获得补贴。', '就业扶持', '0', '0', 1, '2025-07-21 00:00:00', NULL, NULL, '就业扶持政策');

-- ----------------------------
-- Table structure for qrtz_blob_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_blob_triggers`;
CREATE TABLE `qrtz_blob_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `blob_data` blob NULL COMMENT '存放持久化Trigger对象',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_blob_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'Blob类型的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_blob_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_calendars
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_calendars`;
CREATE TABLE `qrtz_calendars`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日历名称',
  `calendar` blob NOT NULL COMMENT '存放持久化calendar对象',
  PRIMARY KEY (`sched_name`, `calendar_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '日历信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_calendars
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_cron_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_cron_triggers`;
CREATE TABLE `qrtz_cron_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `cron_expression` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'cron表达式',
  `time_zone_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时区',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_cron_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'Cron类型的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_cron_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_fired_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_fired_triggers`;
CREATE TABLE `qrtz_fired_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `entry_id` varchar(95) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度器实例id',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度器实例名',
  `fired_time` bigint(0) NOT NULL COMMENT '触发的时间',
  `sched_time` bigint(0) NOT NULL COMMENT '定时器制定的时间',
  `priority` int(0) NOT NULL COMMENT '优先级',
  `state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '任务名称',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '任务组名',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否并发',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否接受恢复执行',
  PRIMARY KEY (`sched_name`, `entry_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '已触发的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_fired_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_job_details
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_job_details`;
CREATE TABLE `qrtz_job_details`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务组名',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '相关介绍',
  `job_class_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '执行任务类名称',
  `is_durable` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '是否持久化',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '是否并发',
  `is_update_data` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '是否更新数据',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '是否接受恢复执行',
  `job_data` blob NULL COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务详细信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_job_details
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_locks
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_locks`;
CREATE TABLE `qrtz_locks`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `lock_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '悲观锁名称',
  PRIMARY KEY (`sched_name`, `lock_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '存储的悲观锁信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_locks
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_paused_trigger_grps
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
CREATE TABLE `qrtz_paused_trigger_grps`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  PRIMARY KEY (`sched_name`, `trigger_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '暂停的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_paused_trigger_grps
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_scheduler_state
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_scheduler_state`;
CREATE TABLE `qrtz_scheduler_state`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '实例名称',
  `last_checkin_time` bigint(0) NOT NULL COMMENT '上次检查时间',
  `checkin_interval` bigint(0) NOT NULL COMMENT '检查间隔时间',
  PRIMARY KEY (`sched_name`, `instance_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '调度器状态表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_scheduler_state
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_simple_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simple_triggers`;
CREATE TABLE `qrtz_simple_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `repeat_count` bigint(0) NOT NULL COMMENT '重复的次数统计',
  `repeat_interval` bigint(0) NOT NULL COMMENT '重复的间隔时间',
  `times_triggered` bigint(0) NOT NULL COMMENT '已经触发的次数',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simple_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '简单触发器的信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_simple_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_simprop_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simprop_triggers`;
CREATE TABLE `qrtz_simprop_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `str_prop_1` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第一个参数',
  `str_prop_2` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第二个参数',
  `str_prop_3` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第三个参数',
  `int_prop_1` int(0) NULL DEFAULT NULL COMMENT 'int类型的trigger的第一个参数',
  `int_prop_2` int(0) NULL DEFAULT NULL COMMENT 'int类型的trigger的第二个参数',
  `long_prop_1` bigint(0) NULL DEFAULT NULL COMMENT 'long类型的trigger的第一个参数',
  `long_prop_2` bigint(0) NULL DEFAULT NULL COMMENT 'long类型的trigger的第二个参数',
  `dec_prop_1` decimal(13, 4) NULL DEFAULT NULL COMMENT 'decimal类型的trigger的第一个参数',
  `dec_prop_2` decimal(13, 4) NULL DEFAULT NULL COMMENT 'decimal类型的trigger的第二个参数',
  `bool_prop_1` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'Boolean类型的trigger的第一个参数',
  `bool_prop_2` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'Boolean类型的trigger的第二个参数',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simprop_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '同步机制的行锁表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_simprop_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_triggers`;
CREATE TABLE `qrtz_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '触发器的名字',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '触发器所属组的名字',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_job_details表job_name的外键',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_job_details表job_group的外键',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '相关介绍',
  `next_fire_time` bigint(0) NULL DEFAULT NULL COMMENT '上一次触发时间（毫秒）',
  `prev_fire_time` bigint(0) NULL DEFAULT NULL COMMENT '下一次触发时间（默认为-1表示不触发）',
  `priority` int(0) NULL DEFAULT NULL COMMENT '优先级',
  `trigger_state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '触发器状态',
  `trigger_type` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '触发器的类型',
  `start_time` bigint(0) NOT NULL COMMENT '开始时间',
  `end_time` bigint(0) NULL DEFAULT NULL COMMENT '结束时间',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '日程表名称',
  `misfire_instr` smallint(0) NULL DEFAULT NULL COMMENT '补偿执行的策略',
  `job_data` blob NULL COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  INDEX `sched_name`(`sched_name`, `job_name`, `job_group`) USING BTREE,
  CONSTRAINT `qrtz_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `job_name`, `job_group`) REFERENCES `qrtz_job_details` (`sched_name`, `job_name`, `job_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '触发器详细信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for recruitment_info
-- ----------------------------
DROP TABLE IF EXISTS `recruitment_info`;
CREATE TABLE `recruitment_info`  (
  `recruitment_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '招聘ID',
  `job_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '职位名称',
  `company_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公司名称',
  `job_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '职位描述',
  `job_requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '职位要求',
  `work_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工作地点',
  `work_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工作类型(全职/兼职/临时)',
  `salary_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '最低薪资',
  `salary_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '最高薪资',
  `salary_unit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'month' COMMENT '薪资单位(hour/day/month)',
  `education_requirement` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学历要求',
  `experience_requirement` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '经验要求',
  `age_min` int(0) NULL DEFAULT NULL COMMENT '最小年龄要求',
  `age_max` int(0) NULL DEFAULT NULL COMMENT '最大年龄要求',
  `gender_requirement` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '性别要求(0男 1女 2不限)',
  `contact_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `recruitment_count` int(0) NULL DEFAULT 1 COMMENT '招聘人数',
  `current_applicants` int(0) NULL DEFAULT 0 COMMENT '当前申请人数',
  `work_start_date` date NULL DEFAULT NULL COMMENT '工作开始日期',
  `work_end_date` date NULL DEFAULT NULL COMMENT '工作结束日期',
  `application_deadline` date NULL DEFAULT NULL COMMENT '申请截止日期',
  `is_urgent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '是否紧急(0否 1是)',
  `is_featured` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '是否推荐(0否 1是)',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0发布中 1已暂停 2已结束）',
  `publisher_user_id` bigint(0) NOT NULL COMMENT '发布者用户ID',
  `company_logo` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '公司logo',
  `job_images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '职位相关图片(JSON格式)',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`recruitment_id`) USING BTREE,
  INDEX `idx_publisher_user_id`(`publisher_user_id`) USING BTREE,
  INDEX `idx_work_location`(`work_location`) USING BTREE,
  INDEX `idx_work_type`(`work_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_application_deadline`(`application_deadline`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '招聘信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of recruitment_info
-- ----------------------------

-- ----------------------------
-- Table structure for recruitment_skill
-- ----------------------------
DROP TABLE IF EXISTS `recruitment_skill`;
CREATE TABLE `recruitment_skill`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `recruitment_id` bigint(0) NOT NULL COMMENT '招聘ID',
  `skill_id` bigint(0) NOT NULL COMMENT '技能ID',
  `skill_level_required` tinyint(0) NULL DEFAULT 1 COMMENT '要求技能等级(1-初级 2-中级 3-高级)',
  `is_required` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '是否必需(0否 1是)',
  `weight` decimal(3, 2) NULL DEFAULT 1.00 COMMENT '权重(0-1)',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_recruitment_skill`(`recruitment_id`, `skill_id`) USING BTREE,
  INDEX `idx_recruitment_id`(`recruitment_id`) USING BTREE,
  INDEX `idx_skill_id`(`skill_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '招聘技能关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of recruitment_skill
-- ----------------------------

-- ----------------------------
-- Table structure for skill_tag
-- ----------------------------
DROP TABLE IF EXISTS `skill_tag`;
CREATE TABLE `skill_tag`  (
  `skill_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '技能ID',
  `skill_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '技能名称',
  `skill_category` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '技能分类',
  `skill_level` tinyint(0) NULL DEFAULT 1 COMMENT '技能等级要求(1-初级 2-中级 3-高级)',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`skill_id`) USING BTREE,
  UNIQUE INDEX `uk_skill_name`(`skill_name`) USING BTREE,
  INDEX `idx_skill_category`(`skill_category`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '技能标签表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of skill_tag
-- ----------------------------

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` int(0) NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '参数配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 1, '2025-06-07 19:23:03', NULL, NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 1, '2025-06-07 19:23:03', NULL, NULL, '初始化密码 123456');
INSERT INTO `sys_config` VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-light', 'Y', 1, '2025-06-07 19:23:03', 1, '2025-06-28 11:08:45', '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` VALUES (4, '账号自助-验证码开关', 'sys.account.captchaEnabled', 'true', 'Y', 1, '2025-06-07 19:23:03', NULL, NULL, '是否开启验证码功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (5, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 1, '2025-06-07 19:23:03', NULL, NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (6, '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 1, '2025-06-07 19:23:03', NULL, NULL, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');
INSERT INTO `sys_config` VALUES (7, '用户管理-初始密码修改策略', 'sys.account.initPasswordModify', '1', 'Y', 1, '2025-06-07 19:23:03', NULL, NULL, '0：初始密码修改策略关闭，没有任何提示，1：提醒用户，如果未修改初始密码，则在登录时就会提醒修改密码对话框');
INSERT INTO `sys_config` VALUES (8, '用户管理-账号密码更新周期', 'sys.account.passwordValidateDays', '0', 'Y', 1, '2025-06-07 19:23:03', NULL, NULL, '密码更新周期（填写数字，数据初始化值为0不限制，若修改必须为大于0小于365的正整数），如果超过这个周期登录系统时，则在登录时就会提醒修改密码对话框');

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint(0) NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int(0) NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 200 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (100, 0, '0', '若依科技', 0, '若依', '15888888888', '<EMAIL>', '0', '0', 1, '2025-06-07 19:23:02', NULL, NULL);

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int(0) NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1084 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '性别男');
INSERT INTO `sys_dict_data` VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '性别女');
INSERT INTO `sys_dict_data` VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '性别未知');
INSERT INTO `sys_dict_data` VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '显示菜单');
INSERT INTO `sys_dict_data` VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '默认分组');
INSERT INTO `sys_dict_data` VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '系统分组');
INSERT INTO `sys_dict_data` VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '系统默认是');
INSERT INTO `sys_dict_data` VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '系统默认否');
INSERT INTO `sys_dict_data` VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '通知');
INSERT INTO `sys_dict_data` VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '公告');
INSERT INTO `sys_dict_data` VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '关闭状态');
INSERT INTO `sys_dict_data` VALUES (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '其他操作');
INSERT INTO `sys_dict_data` VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '新增操作');
INSERT INTO `sys_dict_data` VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '修改操作');
INSERT INTO `sys_dict_data` VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '删除操作');
INSERT INTO `sys_dict_data` VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '授权操作');
INSERT INTO `sys_dict_data` VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '导出操作');
INSERT INTO `sys_dict_data` VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '导入操作');
INSERT INTO `sys_dict_data` VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '强退操作');
INSERT INTO `sys_dict_data` VALUES (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '生成操作');
INSERT INTO `sys_dict_data` VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '清空操作');
INSERT INTO `sys_dict_data` VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (1000, 1, '全职', '全职', 'job_type_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '全职工作');
INSERT INTO `sys_dict_data` VALUES (1001, 2, '兼职', '兼职', 'job_type_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '兼职工作');
INSERT INTO `sys_dict_data` VALUES (1002, 3, '临时工', '临时工', 'job_type_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '临时工作');
INSERT INTO `sys_dict_data` VALUES (1003, 4, '小时工', '小时工', 'job_type_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '小时工作');
INSERT INTO `sys_dict_data` VALUES (1004, 5, '实习', '实习', 'job_type_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '实习工作');
INSERT INTO `sys_dict_data` VALUES (1010, 1, '服务员', '服务员', 'job_category_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '服务员工作');
INSERT INTO `sys_dict_data` VALUES (1011, 2, '保洁', '保洁', 'job_category_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '保洁工作');
INSERT INTO `sys_dict_data` VALUES (1012, 3, '搬运工', '搬运工', 'job_category_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '搬运工作');
INSERT INTO `sys_dict_data` VALUES (1013, 4, '销售', '销售', 'job_category_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '销售工作');
INSERT INTO `sys_dict_data` VALUES (1014, 5, '客服', '客服', 'job_category_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '客服工作');
INSERT INTO `sys_dict_data` VALUES (1015, 6, '配送员', '配送员', 'job_category_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '配送工作');
INSERT INTO `sys_dict_data` VALUES (1016, 7, '厨师', '厨师', 'job_category_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '厨师工作');
INSERT INTO `sys_dict_data` VALUES (1017, 8, '司机', '司机', 'job_category_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '司机工作');
INSERT INTO `sys_dict_data` VALUES (1018, 9, '保安', '保安', 'job_category_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '保安工作');
INSERT INTO `sys_dict_data` VALUES (1019, 10, '其他', '其他', 'job_category_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '其他工作');
INSERT INTO `sys_dict_data` VALUES (1020, 1, '小时', 'hourly', 'salary_type_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '按小时计薪');
INSERT INTO `sys_dict_data` VALUES (1021, 2, '日薪', 'daily', 'salary_type_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '按日计薪');
INSERT INTO `sys_dict_data` VALUES (1022, 3, '月薪', 'monthly', 'salary_type_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '按月计薪');
INSERT INTO `sys_dict_data` VALUES (1023, 4, '计件', 'piece', 'salary_type_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '按件计薪');
INSERT INTO `sys_dict_data` VALUES (1030, 1, '紧急', 'urgent', 'urgency_level_options', 'danger', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '紧急');
INSERT INTO `sys_dict_data` VALUES (1031, 2, '高', 'high', 'urgency_level_options', 'warning', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '高优先级');
INSERT INTO `sys_dict_data` VALUES (1032, 3, '普通', 'normal', 'urgency_level_options', 'info', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '普通优先级');
INSERT INTO `sys_dict_data` VALUES (1033, 4, '低', 'low', 'urgency_level_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '低优先级');
INSERT INTO `sys_dict_data` VALUES (1040, 1, '雇主', 'employer', 'publisher_type_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '雇主');
INSERT INTO `sys_dict_data` VALUES (1041, 2, '中介', 'agency', 'publisher_type_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '中介机构');
INSERT INTO `sys_dict_data` VALUES (1042, 3, '个人', 'individual', 'publisher_type_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '个人');
INSERT INTO `sys_dict_data` VALUES (1050, 1, '不限', 'any', 'gender_requirement_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '性别不限');
INSERT INTO `sys_dict_data` VALUES (1051, 2, '男', 'male', 'gender_requirement_options', 'primary', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '限男性');
INSERT INTO `sys_dict_data` VALUES (1052, 3, '女', 'female', 'gender_requirement_options', 'danger', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '限女性');
INSERT INTO `sys_dict_data` VALUES (1060, 1, '活跃', 'active', 'worker_status_options', 'success', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '活跃状态');
INSERT INTO `sys_dict_data` VALUES (1061, 2, '不活跃', 'inactive', 'worker_status_options', 'info', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '不活跃状态');
INSERT INTO `sys_dict_data` VALUES (1062, 3, '暂停', 'suspended', 'worker_status_options', 'warning', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '暂停状态');
INSERT INTO `sys_dict_data` VALUES (1063, 4, '禁用', 'banned', 'worker_status_options', 'danger', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '禁用状态');
INSERT INTO `sys_dict_data` VALUES (1070, 1, '小学', '小学', 'education_level_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '小学学历');
INSERT INTO `sys_dict_data` VALUES (1071, 2, '初中', '初中', 'education_level_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '初中学历');
INSERT INTO `sys_dict_data` VALUES (1072, 3, '高中', '高中', 'education_level_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '高中学历');
INSERT INTO `sys_dict_data` VALUES (1073, 4, '中专', '中专', 'education_level_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '中专学历');
INSERT INTO `sys_dict_data` VALUES (1074, 5, '大专', '大专', 'education_level_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '大专学历');
INSERT INTO `sys_dict_data` VALUES (1075, 6, '本科', '本科', 'education_level_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '本科学历');
INSERT INTO `sys_dict_data` VALUES (1076, 7, '硕士', '硕士', 'education_level_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '硕士学历');
INSERT INTO `sys_dict_data` VALUES (1077, 8, '博士', '博士', 'education_level_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '博士学历');
INSERT INTO `sys_dict_data` VALUES (1080, 1, '灵活', 'flexible', 'work_time_preference_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '灵活时间');
INSERT INTO `sys_dict_data` VALUES (1081, 2, '固定', 'fixed', 'work_time_preference_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '固定时间');
INSERT INTO `sys_dict_data` VALUES (1082, 3, '兼职', 'part_time', 'work_time_preference_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '兼职时间');
INSERT INTO `sys_dict_data` VALUES (1083, 4, '全职', 'full_time', 'work_time_preference_options', '', '', 'Y', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '全职时间');

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 109 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '用户性别', 'sys_user_sex', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (2, '菜单状态', 'sys_show_hide', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (3, '系统开关', 'sys_normal_disable', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (4, '任务状态', 'sys_job_status', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (5, '任务分组', 'sys_job_group', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (6, '系统是否', 'sys_yes_no', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (7, '通知类型', 'sys_notice_type', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (8, '通知状态', 'sys_notice_status', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (9, '操作类型', 'sys_oper_type', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (10, '系统状态', 'sys_common_status', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '登录状态列表');
INSERT INTO `sys_dict_type` VALUES (100, '工作类型', 'job_type_options', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '工作类型选项');
INSERT INTO `sys_dict_type` VALUES (101, '工作类别', 'job_category_options', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '工作类别选项');
INSERT INTO `sys_dict_type` VALUES (102, '薪资类型', 'salary_type_options', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '薪资类型选项');
INSERT INTO `sys_dict_type` VALUES (103, '紧急程度', 'urgency_level_options', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '紧急程度选项');
INSERT INTO `sys_dict_type` VALUES (104, '发布者类型', 'publisher_type_options', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '发布者类型选项');
INSERT INTO `sys_dict_type` VALUES (105, '性别要求', 'gender_requirement_options', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '性别要求选项');
INSERT INTO `sys_dict_type` VALUES (106, '零工状态', 'worker_status_options', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '零工状态选项');
INSERT INTO `sys_dict_type` VALUES (107, '学历水平', 'education_level_options', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '学历水平选项');
INSERT INTO `sys_dict_type` VALUES (108, '工作时间偏好', 'work_time_preference_options', '0', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '工作时间偏好选项');

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
  `job_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '定时任务调度表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO `sys_job` VALUES (1, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '0/10 * * * * ?', '3', '1', '1', 1, '2025-06-07 19:23:03', 1, '2025-06-28 13:29:07', '');

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
  `job_log_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '日志信息',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '异常信息',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '定时任务调度日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------
INSERT INTO `sys_job_log` VALUES (1, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '系统默认（无参） 总共耗时：6803毫秒', '0', '', '2025-06-28 10:58:28');
INSERT INTO `sys_job_log` VALUES (2, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '系统默认（无参） 总共耗时：10432毫秒', '0', '', '2025-06-28 10:59:35');
INSERT INTO `sys_job_log` VALUES (3, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '系统默认（无参） 总共耗时：58246毫秒', '0', '', '2025-06-28 11:05:19');
INSERT INTO `sys_job_log` VALUES (4, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '系统默认（无参） 总共耗时：12896毫秒', '1', 'java.lang.reflect.InvocationTargetException\r\n	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)\r\n	at java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n	at com.sux.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:61)\r\n	at com.sux.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)\r\n	at com.sux.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)\r\n	at com.sux.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:42)\r\n	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)\r\n	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)\r\nCaused by: com.sux.common.exception.ServiceException: 测试多数据\r\n	at com.sux.system.service.impl.SysConfigServiceImpl.selectConfigByS(SysConfigServiceImpl.java:228)\r\n	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\r\n	at java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)\r\n	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)\r\n	at com.sux.framework.aspectj.DataSourceAspect.around(DataSourceAspect.java:49)\r\n	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\r\n	at java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)\r\n	at org.springframework.aop.aspectj', '2025-06-28 11:07:24');
INSERT INTO `sys_job_log` VALUES (5, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '系统默认（无参） 总共耗时：9823毫秒', '1', 'java.lang.reflect.InvocationTargetException\r\n	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)\r\n	at java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n	at com.sux.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:61)\r\n	at com.sux.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)\r\n	at com.sux.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)\r\n	at com.sux.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:42)\r\n	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)\r\n	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)\r\nCaused by: com.sux.common.exception.ServiceException: 测试多数据\r\n	at com.sux.system.service.impl.SysConfigServiceImpl.selectConfigByS(SysConfigServiceImpl.java:228)\r\n	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\r\n	at java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)\r\n	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)\r\n	at com.sux.framework.aspectj.DataSourceAspect.around(DataSourceAspect.java:49)\r\n	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\r\n	at java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)\r\n	at org.springframework.aop.aspectj', '2025-06-28 11:08:55');

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `info_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作系统',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '提示消息',
  `login_time` datetime(0) NULL DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`) USING BTREE,
  INDEX `idx_sys_logininfor_s`(`status`) USING BTREE,
  INDEX `idx_sys_logininfor_lt`(`login_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 229 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统访问记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_logininfor
-- ----------------------------
INSERT INTO `sys_logininfor` VALUES (100, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-26 20:37:40');
INSERT INTO `sys_logininfor` VALUES (101, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-27 20:22:05');
INSERT INTO `sys_logininfor` VALUES (102, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-27 23:33:11');
INSERT INTO `sys_logininfor` VALUES (103, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-28 10:20:34');
INSERT INTO `sys_logininfor` VALUES (104, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-28 21:57:29');
INSERT INTO `sys_logininfor` VALUES (105, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-29 03:56:16');
INSERT INTO `sys_logininfor` VALUES (106, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码已失效', '2025-06-29 19:42:13');
INSERT INTO `sys_logininfor` VALUES (107, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-06-29 19:42:18');
INSERT INTO `sys_logininfor` VALUES (108, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-29 19:42:22');
INSERT INTO `sys_logininfor` VALUES (109, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-30 19:45:27');
INSERT INTO `sys_logininfor` VALUES (110, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码已失效', '2025-07-09 21:58:11');
INSERT INTO `sys_logininfor` VALUES (111, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-09 22:00:26');
INSERT INTO `sys_logininfor` VALUES (112, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码已失效', '2025-07-09 22:04:20');
INSERT INTO `sys_logininfor` VALUES (113, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-09 22:04:28');
INSERT INTO `sys_logininfor` VALUES (114, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-10 21:46:44');
INSERT INTO `sys_logininfor` VALUES (115, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-10 21:46:59');
INSERT INTO `sys_logininfor` VALUES (116, '从沙发上', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-10 22:24:20');
INSERT INTO `sys_logininfor` VALUES (117, '从沙发上', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-10 22:24:27');
INSERT INTO `sys_logininfor` VALUES (118, '从沙发上', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-10 22:24:48');
INSERT INTO `sys_logininfor` VALUES (119, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-10 22:42:16');
INSERT INTO `sys_logininfor` VALUES (120, '12321453', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 21:03:08');
INSERT INTO `sys_logininfor` VALUES (121, '12321453', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 21:07:31');
INSERT INTO `sys_logininfor` VALUES (122, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-11 21:07:43');
INSERT INTO `sys_logininfor` VALUES (123, '12321453', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '用户不存在/密码错误', '2025-07-11 21:18:32');
INSERT INTO `sys_logininfor` VALUES (124, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 21:18:41');
INSERT INTO `sys_logininfor` VALUES (125, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-07-11 21:21:42');
INSERT INTO `sys_logininfor` VALUES (126, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-11 21:21:47');
INSERT INTO `sys_logininfor` VALUES (127, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 21:21:52');
INSERT INTO `sys_logininfor` VALUES (128, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 21:22:23');
INSERT INTO `sys_logininfor` VALUES (129, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 21:22:34');
INSERT INTO `sys_logininfor` VALUES (130, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 21:22:52');
INSERT INTO `sys_logininfor` VALUES (131, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 21:22:55');
INSERT INTO `sys_logininfor` VALUES (132, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-11 22:14:56');
INSERT INTO `sys_logininfor` VALUES (133, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-11 22:24:54');
INSERT INTO `sys_logininfor` VALUES (134, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-11 22:25:52');
INSERT INTO `sys_logininfor` VALUES (135, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 22:26:22');
INSERT INTO `sys_logininfor` VALUES (136, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 22:26:23');
INSERT INTO `sys_logininfor` VALUES (137, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 22:26:25');
INSERT INTO `sys_logininfor` VALUES (138, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 22:26:26');
INSERT INTO `sys_logininfor` VALUES (139, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 22:26:26');
INSERT INTO `sys_logininfor` VALUES (140, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 22:26:26');
INSERT INTO `sys_logininfor` VALUES (141, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 22:27:02');
INSERT INTO `sys_logininfor` VALUES (142, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:27:41');
INSERT INTO `sys_logininfor` VALUES (143, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:33:18');
INSERT INTO `sys_logininfor` VALUES (144, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:33:24');
INSERT INTO `sys_logininfor` VALUES (145, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-11 22:33:27');
INSERT INTO `sys_logininfor` VALUES (146, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-11 22:33:34');
INSERT INTO `sys_logininfor` VALUES (147, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:33:40');
INSERT INTO `sys_logininfor` VALUES (148, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:33:47');
INSERT INTO `sys_logininfor` VALUES (149, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:34:10');
INSERT INTO `sys_logininfor` VALUES (150, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:34:12');
INSERT INTO `sys_logininfor` VALUES (151, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:34:22');
INSERT INTO `sys_logininfor` VALUES (152, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:34:28');
INSERT INTO `sys_logininfor` VALUES (153, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:34:49');
INSERT INTO `sys_logininfor` VALUES (154, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:35:17');
INSERT INTO `sys_logininfor` VALUES (155, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:35:25');
INSERT INTO `sys_logininfor` VALUES (156, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:36:11');
INSERT INTO `sys_logininfor` VALUES (157, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:37:15');
INSERT INTO `sys_logininfor` VALUES (158, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:37:18');
INSERT INTO `sys_logininfor` VALUES (159, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:41:57');
INSERT INTO `sys_logininfor` VALUES (160, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:42:06');
INSERT INTO `sys_logininfor` VALUES (161, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:42:11');
INSERT INTO `sys_logininfor` VALUES (162, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:43:21');
INSERT INTO `sys_logininfor` VALUES (163, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:43:33');
INSERT INTO `sys_logininfor` VALUES (164, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:43:43');
INSERT INTO `sys_logininfor` VALUES (165, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:44:02');
INSERT INTO `sys_logininfor` VALUES (166, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:44:05');
INSERT INTO `sys_logininfor` VALUES (167, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:44:30');
INSERT INTO `sys_logininfor` VALUES (168, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:44:43');
INSERT INTO `sys_logininfor` VALUES (169, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-11 22:44:59');
INSERT INTO `sys_logininfor` VALUES (170, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-11 22:55:04');
INSERT INTO `sys_logininfor` VALUES (171, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:01:08');
INSERT INTO `sys_logininfor` VALUES (172, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-11 23:01:12');
INSERT INTO `sys_logininfor` VALUES (173, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-11 23:01:20');
INSERT INTO `sys_logininfor` VALUES (174, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:01:32');
INSERT INTO `sys_logininfor` VALUES (175, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:06:22');
INSERT INTO `sys_logininfor` VALUES (176, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:06:25');
INSERT INTO `sys_logininfor` VALUES (177, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:07:29');
INSERT INTO `sys_logininfor` VALUES (178, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:07:31');
INSERT INTO `sys_logininfor` VALUES (179, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:07:37');
INSERT INTO `sys_logininfor` VALUES (180, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:07:41');
INSERT INTO `sys_logininfor` VALUES (181, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:08:38');
INSERT INTO `sys_logininfor` VALUES (182, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:08:40');
INSERT INTO `sys_logininfor` VALUES (183, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:08:43');
INSERT INTO `sys_logininfor` VALUES (184, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:08:45');
INSERT INTO `sys_logininfor` VALUES (185, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:10:08');
INSERT INTO `sys_logininfor` VALUES (186, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:10:10');
INSERT INTO `sys_logininfor` VALUES (187, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:12:04');
INSERT INTO `sys_logininfor` VALUES (188, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:12:06');
INSERT INTO `sys_logininfor` VALUES (189, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:12:31');
INSERT INTO `sys_logininfor` VALUES (190, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:12:34');
INSERT INTO `sys_logininfor` VALUES (191, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:12:42');
INSERT INTO `sys_logininfor` VALUES (192, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:12:44');
INSERT INTO `sys_logininfor` VALUES (193, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:12:55');
INSERT INTO `sys_logininfor` VALUES (194, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:12:58');
INSERT INTO `sys_logininfor` VALUES (195, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:14:18');
INSERT INTO `sys_logininfor` VALUES (196, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:14:20');
INSERT INTO `sys_logininfor` VALUES (197, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:14:29');
INSERT INTO `sys_logininfor` VALUES (198, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:14:31');
INSERT INTO `sys_logininfor` VALUES (199, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:14:37');
INSERT INTO `sys_logininfor` VALUES (200, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:14:38');
INSERT INTO `sys_logininfor` VALUES (201, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:20:09');
INSERT INTO `sys_logininfor` VALUES (202, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:20:10');
INSERT INTO `sys_logininfor` VALUES (203, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:20:29');
INSERT INTO `sys_logininfor` VALUES (204, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:20:31');
INSERT INTO `sys_logininfor` VALUES (205, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:20:40');
INSERT INTO `sys_logininfor` VALUES (206, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:20:41');
INSERT INTO `sys_logininfor` VALUES (207, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:21:17');
INSERT INTO `sys_logininfor` VALUES (208, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:21:43');
INSERT INTO `sys_logininfor` VALUES (209, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:22:12');
INSERT INTO `sys_logininfor` VALUES (210, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:29:09');
INSERT INTO `sys_logininfor` VALUES (211, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:29:16');
INSERT INTO `sys_logininfor` VALUES (212, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-12 13:48:04');
INSERT INTO `sys_logininfor` VALUES (213, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-12 13:54:54');
INSERT INTO `sys_logininfor` VALUES (214, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-12 13:54:57');
INSERT INTO `sys_logininfor` VALUES (215, 'user', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-23 00:04:10');
INSERT INTO `sys_logininfor` VALUES (216, 'admin_3', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-23 00:04:16');
INSERT INTO `sys_logininfor` VALUES (217, 'admin_3', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-23 00:05:22');
INSERT INTO `sys_logininfor` VALUES (218, 'admin_1', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-07-23 00:05:29');
INSERT INTO `sys_logininfor` VALUES (219, 'admin_1', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-07-23 00:05:35');
INSERT INTO `sys_logininfor` VALUES (220, 'admin_1', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-23 00:05:40');
INSERT INTO `sys_logininfor` VALUES (221, 'admin_1', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-23 00:08:57');
INSERT INTO `sys_logininfor` VALUES (222, 'admin_2', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-23 00:09:06');
INSERT INTO `sys_logininfor` VALUES (223, 'admin_2', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-23 00:09:36');
INSERT INTO `sys_logininfor` VALUES (224, 'admin_3', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-23 00:09:45');
INSERT INTO `sys_logininfor` VALUES (225, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-23 13:54:07');
INSERT INTO `sys_logininfor` VALUES (226, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-23 18:41:41');
INSERT INTO `sys_logininfor` VALUES (227, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-23 18:41:45');
INSERT INTO `sys_logininfor` VALUES (228, '企业就业培训申请', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-23 18:47:08');
INSERT INTO `sys_logininfor` VALUES (229, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-23 21:53:25');
INSERT INTO `sys_logininfor` VALUES (230, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-23 21:55:52');
INSERT INTO `sys_logininfor` VALUES (231, '企业培训申请', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-23 22:38:34');
INSERT INTO `sys_logininfor` VALUES (232, '企业培训申请', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-23 22:56:54');
INSERT INTO `sys_logininfor` VALUES (233, '零工市场', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-23 22:57:01');
INSERT INTO `sys_logininfor` VALUES (234, '零工市场', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-23 23:05:31');
INSERT INTO `sys_logininfor` VALUES (235, '企业就业培训管理', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-07-23 23:05:37');
INSERT INTO `sys_logininfor` VALUES (236, '企业就业培训管理', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-23 23:05:41');
INSERT INTO `sys_logininfor` VALUES (237, '企业就业培训管理', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-24 00:01:54');
INSERT INTO `sys_logininfor` VALUES (238, '零工市场', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-07-24 00:02:23');
INSERT INTO `sys_logininfor` VALUES (239, '零工市场', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-07-24 00:02:27');
INSERT INTO `sys_logininfor` VALUES (240, '零工市场', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-24 00:02:33');

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint(0) NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int(0) NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '路由名称',
  `is_frame` int(0) NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int(0) NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5069 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '菜单权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 0, 99, 'system', NULL, '', '', 1, 0, 'M', '0', '0', '', 'system', 1, '2025-06-07 19:23:02', 1, '2025-07-22 23:59:02', '系统管理目录');
INSERT INTO `sys_menu` VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 1, '2025-06-07 19:23:02', 1, NULL, '用户管理菜单');
INSERT INTO `sys_menu` VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 1, '2025-06-07 19:23:02', 1, NULL, '角色管理菜单');
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 1, '2025-06-07 19:23:02', 1, NULL, '菜单管理菜单');
INSERT INTO `sys_menu` VALUES (103, '部门管理', 1, 4, 'dept', 'system/dept/index', '', '', 1, 0, 'C', '0', '1', 'system:dept:list', 'tree', 1, '2025-06-07 19:23:02', 1, '2025-07-22 23:58:52', '部门管理菜单');
INSERT INTO `sys_menu` VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', '', '', 1, 0, 'C', '0', '1', 'system:post:list', 'post', 1, '2025-06-07 19:23:02', 1, '2025-07-22 23:58:45', '岗位管理菜单');
INSERT INTO `sys_menu` VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', '', '', 1, 0, 'C', '0', '1', 'system:dict:list', 'dict', 1, '2025-06-07 19:23:02', 1, '2025-07-22 23:58:41', '字典管理菜单');
INSERT INTO `sys_menu` VALUES (110, '定时任务', 1, 10, 'job', 'system/job/index', '', '', 1, 0, 'C', '0', '1', 'monitor:job:list', 'job', 1, '2025-06-07 19:23:02', 1, '2025-07-22 23:58:37', '定时任务菜单');
INSERT INTO `sys_menu` VALUES (1000, '用户查询', 100, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1001, '用户新增', 100, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1002, '用户修改', 100, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1003, '用户删除', 100, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1004, '用户导出', 100, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1005, '用户导入', 100, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1006, '重置密码', 100, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1007, '角色查询', 101, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1008, '角色新增', 101, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1009, '角色修改', 101, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1010, '角色删除', 101, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1011, '角色导出', 101, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1012, '菜单查询', 102, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1013, '菜单新增', 102, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1014, '菜单修改', 102, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1015, '菜单删除', 102, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1016, '部门查询', 103, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1017, '部门新增', 103, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1018, '部门修改', 103, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1019, '部门删除', 103, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1020, '岗位查询', 104, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1021, '岗位新增', 104, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1022, '岗位修改', 104, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1023, '岗位删除', 104, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1024, '岗位导出', 104, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1025, '字典查询', 105, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1026, '字典新增', 105, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1027, '字典修改', 105, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1028, '字典删除', 105, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1029, '字典导出', 105, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1049, '任务查询', 110, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1050, '任务新增', 110, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1051, '任务修改', 110, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1052, '任务删除', 110, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1053, '状态修改', 110, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1054, '任务导出', 110, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3000, '企业就业服务', 0, 1, 'policy', NULL, '', '', 1, 0, 'M', '0', '0', '', 'documentation', 1, '2025-07-21 00:00:00', 1, '2025-07-23 00:06:32', '政策管理目录');
INSERT INTO `sys_menu` VALUES (3001, '服务信息', 3000, 1, 'policy', 'policy/policy/index', '', '', 1, 0, 'C', '0', '0', 'policy:info:list', 'form', 1, '2025-07-21 00:00:00', 1, '2025-07-23 00:07:44', '政策信息菜单');
INSERT INTO `sys_menu` VALUES (3002, '服务审核', 3000, 2, 'policyPlan', 'policy/policyPlan/index', '', '', 1, 0, 'C', '0', '0', 'policy:application:list', 'edit', 1, '2025-07-21 00:00:00', 1, '2025-07-23 14:09:13', '政策申请菜单');
INSERT INTO `sys_menu` VALUES (3003, '培训管理', 4048, 3, 'order', 'order/index', '', '', 1, 0, 'C', '0', '0', 'training:order:list', 'skill', 1, '2025-07-22 00:00:00', 1, '2025-07-23 22:09:17', '培训订单菜单');
INSERT INTO `sys_menu` VALUES (3010, '政策信息查询', 3001, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'policy:info:query', '#', 1, '2025-07-21 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3011, '政策信息新增', 3001, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'policy:info:add', '#', 1, '2025-07-21 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3012, '政策信息修改', 3001, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'policy:info:edit', '#', 1, '2025-07-21 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3013, '政策信息删除', 3001, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'policy:info:remove', '#', 1, '2025-07-21 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3014, '政策信息导出', 3001, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'policy:info:export', '#', 1, '2025-07-21 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3030, '培训订单查询', 3003, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'training:order:query', '#', 1, '2025-07-22 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3031, '培训订单新增', 3003, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'training:order:add', '#', 1, '2025-07-22 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3032, '培训订单修改', 3003, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'training:order:edit', '#', 1, '2025-07-22 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3033, '培训订单删除', 3003, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'training:order:remove', '#', 1, '2025-07-22 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3034, '培训订单导出', 3003, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'training:order:export', '#', 1, '2025-07-22 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3035, '培训订单发布', 3003, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'training:order:publish', '#', 1, '2025-07-22 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3036, '培训订单取消', 3003, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'training:order:cancel', '#', 1, '2025-07-22 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4001, '用工信息', 5000, 6, 'employment', 'place/employment/index', '', '', 1, 0, 'C', '0', '0', 'place:employment:list', 'peoples', 1, '2025-07-23 23:54:30', 1, '2025-07-24 00:01:25', '用工信息管理菜单');
INSERT INTO `sys_menu` VALUES (4002, '场地信息', 5000, 7, 'info', 'place/info/index', '', '', 1, 0, 'C', '0', '0', 'place:info:list', 'component', 1, '2025-07-23 23:54:30', 1, '2025-07-24 00:01:38', '场地信息管理菜单');
INSERT INTO `sys_menu` VALUES (4045, '初审', 3002, 0, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'policy:application:first-review', '#', 1, '2025-07-23 00:00:32', NULL, NULL, '');
INSERT INTO `sys_menu` VALUES (4046, '终审', 3002, 0, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'policy:application:final-review', '#', 1, '2025-07-23 00:00:44', NULL, NULL, '');
INSERT INTO `sys_menu` VALUES (4047, '企业就业服务申请', 0, 1, 'policyS', 'policy/policyS/index', NULL, '', 1, 0, 'M', '0', '0', '', 'button', 1, '2025-07-23 00:02:55', 1, '2025-07-23 00:06:17', '');
INSERT INTO `sys_menu` VALUES (4048, '就业培训管理', 0, 2, 'order', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'clipboard', 1, '2025-07-23 00:11:03', 1, '2025-07-23 00:11:13', '');
INSERT INTO `sys_menu` VALUES (4101, '用工信息查询', 4001, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'place:employment:query', '#', 1, '2025-07-23 23:54:30', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4102, '用工信息新增', 4001, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'place:employment:add', '#', 1, '2025-07-23 23:54:30', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4103, '用工信息修改', 4001, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'place:employment:edit', '#', 1, '2025-07-23 23:54:30', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4104, '用工信息删除', 4001, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'place:employment:remove', '#', 1, '2025-07-23 23:54:30', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4105, '用工信息导出', 4001, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'place:employment:export', '#', 1, '2025-07-23 23:54:30', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4106, '用工信息审核', 4001, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'place:employment:review', '#', 1, '2025-07-23 23:54:30', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4107, '用工信息发布', 4001, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'place:employment:publish', '#', 1, '2025-07-23 23:54:30', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4108, '用工信息统计', 4001, 8, '', '', '', '', 1, 0, 'F', '0', '0', 'place:employment:statistics', '#', 1, '2025-07-23 23:54:30', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4201, '场地信息查询', 4002, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'place:info:query', '#', 1, '2025-07-23 23:54:30', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4202, '场地信息新增', 4002, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'place:info:add', '#', 1, '2025-07-23 23:54:30', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4203, '场地信息修改', 4002, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'place:info:edit', '#', 1, '2025-07-23 23:54:30', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4204, '场地信息删除', 4002, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'place:info:remove', '#', 1, '2025-07-23 23:54:30', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4205, '场地信息导出', 4002, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'place:info:export', '#', 1, '2025-07-23 23:54:30', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4206, '场地信息审核', 4002, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'place:info:review', '#', 1, '2025-07-23 23:54:30', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4207, '场地信息统计', 4002, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'place:info:statistics', '#', 1, '2025-07-23 23:54:30', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (5000, '零工市场', 0, 5, 'job', NULL, '', '', 1, 0, 'M', '0', '0', '', 'peoples', 1, '2025-07-22 00:00:00', 1, '2025-07-23 23:29:31', '招聘管理目录');
INSERT INTO `sys_menu` VALUES (5001, '招聘信息', 5000, 1, 'posting', 'zhaop/index', '', '', 1, 0, 'C', '0', '0', 'job:posting:list', 'post', 1, '2025-07-22 00:00:00', 1, '2025-07-24 00:01:42', '招聘信息管理菜单');
INSERT INTO `sys_menu` VALUES (5002, '零工管理', 5000, 2, 'worker', 'zhaop/worker', '', '', 1, 0, 'C', '0', '0', 'job:worker:list', 'user', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '零工管理菜单');
INSERT INTO `sys_menu` VALUES (5010, '招聘信息查询', 5001, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:query', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5011, '招聘信息新增', 5001, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:add', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5012, '招聘信息修改', 5001, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:edit', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5013, '招聘信息删除', 5001, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:remove', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5014, '招聘信息导出', 5001, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:export', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5015, '招聘信息发布', 5001, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:publish', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5016, '招聘信息暂停', 5001, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:pause', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5017, '招聘信息关闭', 5001, 8, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:close', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5018, '招聘信息完成', 5001, 9, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:complete', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5020, '零工信息查询', 5002, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:query', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5021, '零工信息新增', 5002, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:add', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5022, '零工信息修改', 5002, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:edit', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5023, '零工信息删除', 5002, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:remove', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5024, '零工信息导出', 5002, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:export', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5025, '零工信息激活', 5002, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:activate', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5026, '零工信息停用', 5002, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:deactivate', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5027, '零工信息暂停', 5002, 8, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:suspend', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5028, '零工信息禁用', 5002, 9, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:ban', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5029, '零工信息验证', 5002, 10, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:verify', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5030, '零工评分', 5002, 11, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:rate', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5031, '零工统计更新', 5002, 12, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:update-stats', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5059, '个人培训申请', 0, 3, 'order/application/signup', 'order/application/signup', NULL, '', 1, 0, 'M', '0', '0', '', 'edit', 1, '2025-07-23 14:14:09', 1, '2025-07-23 23:27:35', '');
INSERT INTO `sys_menu` VALUES (5060, '个人培训报名记录', 4048, 4, 'application', 'order/application/index', NULL, '', 1, 0, 'C', '0', '0', 'training:application:list', 'form', 1, '2025-07-23 16:36:51', 1, '2025-07-23 18:43:25', '培训报名管理菜单');
INSERT INTO `sys_menu` VALUES (5061, '培训报名查询', 5060, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'training:application:query', '#', 1, '2025-07-23 16:36:51', 1, '2025-07-23 16:36:51', '');
INSERT INTO `sys_menu` VALUES (5062, '培训报名新增', 5060, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'training:application:add', '#', 1, '2025-07-23 16:36:51', 1, '2025-07-23 16:36:51', '');
INSERT INTO `sys_menu` VALUES (5063, '培训报名修改', 5060, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'training:application:edit', '#', 1, '2025-07-23 16:36:51', 1, '2025-07-23 16:36:51', '');
INSERT INTO `sys_menu` VALUES (5064, '培训报名删除', 5060, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'training:application:remove', '#', 1, '2025-07-23 16:36:51', 1, '2025-07-23 16:36:51', '');
INSERT INTO `sys_menu` VALUES (5065, '培训报名导出', 5060, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'training:application:export', '#', 1, '2025-07-23 16:36:51', 1, '2025-07-23 16:36:51', '');
INSERT INTO `sys_menu` VALUES (5066, '培训报名审核', 5060, 6, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'training:application:review', '#', 1, '2025-07-23 16:36:51', 1, '2025-07-23 16:36:51', '');
INSERT INTO `sys_menu` VALUES (5067, '培训报名取消', 5060, 7, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'training:application:cancel', '#', 1, '2025-07-23 16:36:51', 1, '2025-07-23 16:36:51', '');
INSERT INTO `sys_menu` VALUES (5069, '机构培训申请', 0, 3, 'order/application/institution', 'order/application/institution-apply', NULL, '', 1, 0, 'M', '0', '0', '', 'component', 1, '2025-07-23 21:57:14', 1, '2025-07-23 23:27:24', '');
INSERT INTO `sys_menu` VALUES (5070, '培训机构申请管理', 4048, 5, 'institution-application', 'order/application/institution-management', NULL, '', 1, 0, 'C', '0', '0', 'training:institution:application:list', 'peoples', 1, '2025-07-23 22:08:50', 1, '2025-07-23 22:09:10', '培训机构申请管理菜单');
INSERT INTO `sys_menu` VALUES (5071, '培训机构申请查询', 5070, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'training:institution:application:query', '#', 1, '2025-07-23 22:08:50', 1, '2025-07-23 22:08:50', '');
INSERT INTO `sys_menu` VALUES (5072, '培训机构申请新增', 5070, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'training:institution:application:add', '#', 1, '2025-07-23 22:08:50', 1, '2025-07-23 22:08:50', '');
INSERT INTO `sys_menu` VALUES (5073, '培训机构申请修改', 5070, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'training:institution:application:edit', '#', 1, '2025-07-23 22:08:50', 1, '2025-07-23 22:08:50', '');
INSERT INTO `sys_menu` VALUES (5074, '培训机构申请删除', 5070, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'training:institution:application:remove', '#', 1, '2025-07-23 22:08:50', 1, '2025-07-23 22:08:50', '');
INSERT INTO `sys_menu` VALUES (5075, '培训机构申请导出', 5070, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'training:institution:application:export', '#', 1, '2025-07-23 22:08:50', 1, '2025-07-23 22:08:50', '');
INSERT INTO `sys_menu` VALUES (5076, '培训机构申请审核', 5070, 6, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'training:institution:application:review', '#', 1, '2025-07-23 22:08:50', 1, '2025-07-23 22:08:50', '');
INSERT INTO `sys_menu` VALUES (5077, '培训机构申请取消', 5070, 7, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'training:institution:application:cancel', '#', 1, '2025-07-23 22:08:50', 1, '2025-07-23 22:08:50', '');

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` int(0) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob NULL COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '通知公告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_notice
-- ----------------------------
INSERT INTO `sys_notice` VALUES (1, '温馨提醒：2018-07-01 若依新版本发布啦', '2', 0xE696B0E78988E69CACE58685E5AEB9, '0', 1, '2025-06-07 19:23:03', NULL, NULL, '管理员');
INSERT INTO `sys_notice` VALUES (2, '维护通知：2018-07-01 若依系统凌晨维护', '1', 0xE7BBB4E68AA4E58685E5AEB9, '0', 1, '2025-06-07 19:23:03', NULL, NULL, '管理员');

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '模块标题',
  `business_type` int(0) NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int(0) NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '返回参数',
  `status` int(0) NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime(0) NULL DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint(0) NULL DEFAULT 0 COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`) USING BTREE,
  INDEX `idx_sys_oper_log_bt`(`business_type`) USING BTREE,
  INDEX `idx_sys_oper_log_s`(`status`) USING BTREE,
  INDEX `idx_sys_oper_log_ot`(`oper_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 250 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '操作日志记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_oper_log
-- ----------------------------
INSERT INTO `sys_oper_log` VALUES (100, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1046', '127.0.0.1', '内网IP', '1046', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:41', 139);
INSERT INTO `sys_oper_log` VALUES (101, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1047', '127.0.0.1', '内网IP', '1047', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:49', 20);
INSERT INTO `sys_oper_log` VALUES (102, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1048', '127.0.0.1', '内网IP', '1048', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:50', 18);
INSERT INTO `sys_oper_log` VALUES (103, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1055', '127.0.0.1', '内网IP', '1055', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:52', 16);
INSERT INTO `sys_oper_log` VALUES (104, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1056', '127.0.0.1', '内网IP', '1056', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:53', 16);
INSERT INTO `sys_oper_log` VALUES (105, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1057', '127.0.0.1', '内网IP', '1057', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:54', 19);
INSERT INTO `sys_oper_log` VALUES (106, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1058', '127.0.0.1', '内网IP', '1058', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:56', 17);
INSERT INTO `sys_oper_log` VALUES (107, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1059', '127.0.0.1', '内网IP', '1059', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:58', 18);
INSERT INTO `sys_oper_log` VALUES (108, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1060', '127.0.0.1', '内网IP', '1060', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:59', 18);
INSERT INTO `sys_oper_log` VALUES (109, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/job/index\",\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"job\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":110,\"menuName\":\"定时任务\",\"menuType\":\"C\",\"orderNum\":10,\"params\":{},\"parentId\":1,\"path\":\"job\",\"perms\":\"monitor:job:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:26:10', 60);
INSERT INTO `sys_oper_log` VALUES (110, '参数管理', 2, 'com.sux.web.controller.system.SysConfigController.edit()', 'PUT', 1, 'admin', NULL, '/system/config', '127.0.0.1', '内网IP', '{\"configId\":3,\"configKey\":\"sys.index.sideTheme\",\"configName\":\"主框架页-侧边栏主题\",\"configType\":\"Y\",\"configValue\":\"theme-light\",\"createId\":1,\"createTime\":\"2025-06-07 19:23:03\",\"params\":{},\"remark\":\"深色主题theme-dark，浅色主题theme-light\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 20:40:21', 42);
INSERT INTO `sys_oper_log` VALUES (111, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createId\":1,\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"系统案例\",\"menuType\":\"C\",\"orderNum\":99,\"params\":{},\"parentId\":0,\"path\":\"1\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 20:44:10', 29);
INSERT INTO `sys_oper_log` VALUES (112, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/config/index\",\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"edit\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":106,\"menuName\":\"参数设置\",\"menuType\":\"C\",\"orderNum\":7,\"params\":{},\"parentId\":1,\"path\":\"config\",\"perms\":\"system:config:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 20:48:33', 23);
INSERT INTO `sys_oper_log` VALUES (113, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createId\":1,\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"使用案例\",\"menuType\":\"M\",\"orderNum\":99,\"params\":{},\"parentId\":0,\"path\":\"frame\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 20:52:56', 11);
INSERT INTO `sys_oper_log` VALUES (114, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"frame/vue-template/index\",\"createId\":1,\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"前端表单及组件集成\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2000,\"path\":\"vue-template\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 20:54:24', 13);
INSERT INTO `sys_oper_log` VALUES (115, '用户头像', 2, 'com.sux.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2025/06/26/avatar_20250626210632A001.png\",\"code\":200}', 0, NULL, '2025-06-26 21:06:32', 333);
INSERT INTO `sys_oper_log` VALUES (116, '用户头像', 2, 'com.sux.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2025/06/26/avatar_20250626210638A002.png\",\"code\":200}', 0, NULL, '2025-06-26 21:06:38', 16);
INSERT INTO `sys_oper_log` VALUES (117, '用户头像', 2, 'com.sux.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】可爱-可爱猫-大眼猫_20250626224714A019.png\",\"code\":200}', 0, NULL, '2025-06-26 22:47:14', 22);
INSERT INTO `sys_oper_log` VALUES (118, '用户头像', 2, 'com.sux.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png\",\"code\":200}', 0, NULL, '2025-06-26 22:48:03', 9);
INSERT INTO `sys_oper_log` VALUES (119, '字典类型', 1, 'com.sux.web.controller.system.SysDictTypeController.add()', 'POST', 1, 'admin', NULL, '/system/dict/type', '127.0.0.1', '内网IP', '{\"createId\":1,\"dictName\":\"a\",\"dictType\":\"s\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 21:21:53', 118);
INSERT INTO `sys_oper_log` VALUES (120, '字典类型', 3, 'com.sux.web.controller.system.SysDictTypeController.remove()', 'DELETE', 1, 'admin', NULL, '/system/dict/type/100', '127.0.0.1', '内网IP', '[100]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 21:21:56', 31);
INSERT INTO `sys_oper_log` VALUES (121, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.edit()', 'PUT', 1, 'admin', NULL, '/monitor/job', '127.0.0.1', '内网IP', '{\"concurrent\":\"1\",\"createId\":1,\"createTime\":\"2025-06-07 19:23:03\",\"cronExpression\":\"0/10 * * * * ?\",\"invokeTarget\":\"ryTask.ryNoParams\",\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"jobName\":\"系统默认（无参）\",\"misfirePolicy\":\"2\",\"nextValidTime\":\"2025-06-27 21:27:50\",\"params\":{},\"remark\":\"\",\"status\":\"1\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 21:27:49', 26);
INSERT INTO `sys_oper_log` VALUES (122, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.edit()', 'PUT', 1, 'admin', NULL, '/monitor/job', '127.0.0.1', '内网IP', '{\"concurrent\":\"1\",\"createId\":1,\"createTime\":\"2025-06-07 19:23:03\",\"cronExpression\":\"0/10 * * * * ?\",\"invokeTarget\":\"ryTask.ryNoParams\",\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"jobName\":\"系统默认（无参）\",\"misfirePolicy\":\"3\",\"nextValidTime\":\"2025-06-27 21:28:00\",\"params\":{},\"remark\":\"\",\"status\":\"1\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 21:27:53', 12);
INSERT INTO `sys_oper_log` VALUES (123, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.changeStatus()', 'PUT', 1, 'admin', NULL, '/system/role/changeStatus', '127.0.0.1', '内网IP', '{\"admin\":false,\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"status\":\"1\",\"updateId\":1}', '{\"msg\":\"操作失败\",\"code\":500}', 0, NULL, '2025-06-27 21:32:00', 12);
INSERT INTO `sys_oper_log` VALUES (124, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-06-07 19:23:02\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2001],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 21:34:38', 22);
INSERT INTO `sys_oper_log` VALUES (125, '字典类型', 9, 'com.sux.web.controller.system.SysDictTypeController.refreshCache()', 'DELETE', 1, 'admin', NULL, '/system/dict/type/refreshCache', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 21:40:33', 25);
INSERT INTO `sys_oper_log` VALUES (126, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-06-07 19:23:02\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,2000,2001],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 21:41:29', 21);
INSERT INTO `sys_oper_log` VALUES (127, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/config/index\",\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"edit\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":106,\"menuName\":\"参数设置\",\"menuType\":\"C\",\"orderNum\":7,\"params\":{},\"parentId\":1,\"path\":\"config\",\"perms\":\"system:config:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 21:45:01', 13);
INSERT INTO `sys_oper_log` VALUES (128, '定时任务', 3, 'com.sux.quartz.controller.SysJobController.remove()', 'DELETE', 1, 'admin', NULL, '/monitor/job/2', '127.0.0.1', '内网IP', '[2]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 10:55:11', 81);
INSERT INTO `sys_oper_log` VALUES (129, '定时任务', 3, 'com.sux.quartz.controller.SysJobController.remove()', 'DELETE', 1, 'admin', NULL, '/monitor/job/3', '127.0.0.1', '内网IP', '[3]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 10:55:12', 20);
INSERT INTO `sys_oper_log` VALUES (130, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.run()', 'PUT', 1, 'admin', NULL, '/monitor/job/run', '127.0.0.1', '内网IP', '{\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"misfirePolicy\":\"0\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 10:58:28', 62);
INSERT INTO `sys_oper_log` VALUES (131, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.run()', 'PUT', 1, 'admin', NULL, '/monitor/job/run', '127.0.0.1', '内网IP', '{\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"misfirePolicy\":\"0\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 10:59:35', 55);
INSERT INTO `sys_oper_log` VALUES (132, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.run()', 'PUT', 1, 'admin', NULL, '/monitor/job/run', '127.0.0.1', '内网IP', '{\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"misfirePolicy\":\"0\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 11:05:19', 22);
INSERT INTO `sys_oper_log` VALUES (133, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.run()', 'PUT', 1, 'admin', NULL, '/monitor/job/run', '127.0.0.1', '内网IP', '{\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"misfirePolicy\":\"0\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 11:07:24', 23);
INSERT INTO `sys_oper_log` VALUES (134, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.run()', 'PUT', 1, 'admin', NULL, '/monitor/job/run', '127.0.0.1', '内网IP', '{\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"misfirePolicy\":\"0\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 11:08:55', 24);
INSERT INTO `sys_oper_log` VALUES (135, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.edit()', 'PUT', 1, 'admin', NULL, '/monitor/job', '127.0.0.1', '内网IP', '{\"concurrent\":\"0\",\"createId\":1,\"createTime\":\"2025-06-07 19:23:03\",\"cronExpression\":\"0/10 * * * * ?\",\"invokeTarget\":\"ryTask.ryNoParams\",\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"jobName\":\"系统默认（无参）\",\"misfirePolicy\":\"3\",\"nextValidTime\":\"2025-06-28 13:29:10\",\"params\":{},\"remark\":\"\",\"status\":\"1\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 13:29:05', 39);
INSERT INTO `sys_oper_log` VALUES (136, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.edit()', 'PUT', 1, 'admin', NULL, '/monitor/job', '127.0.0.1', '内网IP', '{\"concurrent\":\"1\",\"createId\":1,\"createTime\":\"2025-06-07 19:23:03\",\"cronExpression\":\"0/10 * * * * ?\",\"invokeTarget\":\"ryTask.ryNoParams\",\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"jobName\":\"系统默认（无参）\",\"misfirePolicy\":\"3\",\"nextValidTime\":\"2025-06-28 13:29:10\",\"params\":{},\"remark\":\"\",\"status\":\"1\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 13:29:07', 8);
INSERT INTO `sys_oper_log` VALUES (137, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"vue-template/index\",\"createTime\":\"2025-06-26 20:54:24\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2001,\"menuName\":\"前端表单及组件集成\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2000,\"path\":\"vue-template\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 14:15:06', 56);
INSERT INTO `sys_oper_log` VALUES (138, '用户管理', 2, 'com.sux.web.controller.system.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"avatar\":\"\",\"createId\":1,\"createTime\":\"2025-06-07 19:23:02\",\"delFlag\":\"0\",\"dept\":{\"children\":[],\"deptId\":105,\"params\":{}},\"deptId\":105,\"email\":\"<EMAIL>\",\"loginDate\":\"2025-06-07 19:23:02\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"若依\",\"params\":{},\"phonenumber\":\"15666666666\",\"postIds\":[1],\"pwdUpdateDate\":\"2025-06-07 19:23:02\",\"remark\":\"测试员\",\"roleIds\":[2],\"roles\":[{\"admin\":false,\"dataScope\":\"2\",\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\"}],\"sex\":\"1\",\"status\":\"0\",\"updateId\":1,\"userId\":2,\"userName\":\"ry\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 15:59:30', 215);
INSERT INTO `sys_oper_log` VALUES (139, '用户管理', 2, 'com.sux.web.controller.system.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"avatar\":\"\",\"createId\":1,\"createTime\":\"2025-06-07 19:23:02\",\"delFlag\":\"0\",\"dept\":{\"children\":[],\"deptId\":105,\"params\":{}},\"deptId\":105,\"email\":\"<EMAIL>\",\"loginDate\":\"2025-06-07 19:23:02\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"若依\",\"params\":{},\"phonenumber\":\"15666666666\",\"postIds\":[],\"pwdUpdateDate\":\"2025-06-07 19:23:02\",\"remark\":\"测试员\",\"roleIds\":[2],\"roles\":[{\"admin\":false,\"dataScope\":\"2\",\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\"}],\"sex\":\"1\",\"status\":\"0\",\"updateId\":1,\"userId\":2,\"userName\":\"ry\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 15:59:34', 33);
INSERT INTO `sys_oper_log` VALUES (140, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/106', '127.0.0.1', '内网IP', '106', '{\"msg\":\"存在子菜单,不允许删除\",\"code\":601}', 0, NULL, '2025-06-28 16:19:58', 6);
INSERT INTO `sys_oper_log` VALUES (141, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1030', '127.0.0.1', '内网IP', '1030', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 16:20:04', 14);
INSERT INTO `sys_oper_log` VALUES (142, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1031', '127.0.0.1', '内网IP', '1031', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 16:20:06', 12);
INSERT INTO `sys_oper_log` VALUES (143, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1032', '127.0.0.1', '内网IP', '1032', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 16:20:07', 14);
INSERT INTO `sys_oper_log` VALUES (144, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1033', '127.0.0.1', '内网IP', '1033', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 16:20:09', 11);
INSERT INTO `sys_oper_log` VALUES (145, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1034', '127.0.0.1', '内网IP', '1034', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 16:20:11', 15);
INSERT INTO `sys_oper_log` VALUES (146, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/106', '127.0.0.1', '内网IP', '106', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 16:20:12', 13);
INSERT INTO `sys_oper_log` VALUES (147, '角色管理', 4, 'com.sux.web.controller.system.SysRoleController.selectAuthUserAll()', 'PUT', 1, 'admin', NULL, '/system/role/authUser/selectAll', '127.0.0.1', '内网IP', '{\"roleId\":\"2\",\"userIds\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 16:39:27', 20);
INSERT INTO `sys_oper_log` VALUES (148, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-06-07 19:23:02\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,2000,2001],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 16:39:37', 49);
INSERT INTO `sys_oper_log` VALUES (149, '用户管理', 5, 'com.sux.web.controller.system.SysUserController.export()', 'POST', 1, 'admin', NULL, '/system/user/export', '127.0.0.1', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-28 16:41:57', 1461);
INSERT INTO `sys_oper_log` VALUES (150, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2025-06-26 20:52:56\",\"icon\":\"component\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2000,\"menuName\":\"使用案例\",\"menuType\":\"M\",\"orderNum\":99,\"params\":{},\"parentId\":0,\"path\":\"frame\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 17:01:00', 16);
INSERT INTO `sys_oper_log` VALUES (151, '岗位管理', 2, 'com.sux.web.controller.system.SysPostController.edit()', 'PUT', 1, 'admin', NULL, '/system/post', '127.0.0.1', '内网IP', '{\"createId\":1,\"createTime\":\"2025-06-07 19:23:02\",\"flag\":false,\"params\":{},\"postCode\":\"ceo\",\"postId\":1,\"postName\":\"董事长\",\"postSort\":1,\"remark\":\"\",\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 17:15:24', 16);
INSERT INTO `sys_oper_log` VALUES (152, '岗位管理', 2, 'com.sux.web.controller.system.SysPostController.edit()', 'PUT', 1, 'admin', NULL, '/system/post', '127.0.0.1', '内网IP', '{\"createId\":1,\"createTime\":\"2025-06-07 19:23:02\",\"flag\":false,\"params\":{},\"postCode\":\"ceo\",\"postId\":1,\"postName\":\"董事长\",\"postSort\":1,\"remark\":\"\",\"status\":\"1\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 17:15:31', 11);
INSERT INTO `sys_oper_log` VALUES (153, '岗位管理', 2, 'com.sux.web.controller.system.SysPostController.edit()', 'PUT', 1, 'admin', NULL, '/system/post', '127.0.0.1', '内网IP', '{\"createId\":1,\"createTime\":\"2025-06-07 19:23:02\",\"flag\":false,\"params\":{},\"postCode\":\"ceo\",\"postId\":1,\"postName\":\"董事长\",\"postSort\":1,\"remark\":\"\",\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 17:15:34', 7);
INSERT INTO `sys_oper_log` VALUES (154, '字典类型', 9, 'com.sux.web.controller.system.SysDictTypeController.refreshCache()', 'DELETE', 1, 'admin', NULL, '/system/dict/type/refreshCache', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 17:17:11', 35);
INSERT INTO `sys_oper_log` VALUES (155, '用户管理', 2, 'com.sux.web.controller.system.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"avatar\":\"\",\"createId\":1,\"createTime\":\"2025-06-07 19:23:02\",\"delFlag\":\"0\",\"dept\":{\"children\":[],\"deptId\":105,\"params\":{}},\"deptId\":105,\"email\":\"<EMAIL>\",\"loginDate\":\"2025-06-07 19:23:02\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"若依\",\"params\":{},\"phonenumber\":\"15666666666\",\"postIds\":[],\"pwdUpdateDate\":\"2025-06-07 19:23:02\",\"remark\":\"测试员\",\"roleIds\":[2],\"roles\":[{\"admin\":false,\"dataScope\":\"2\",\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\"}],\"sex\":\"1\",\"status\":\"0\",\"updateId\":1,\"userId\":2,\"userName\":\"ry\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-29 03:56:41', 87);
INSERT INTO `sys_oper_log` VALUES (156, '用户头像', 2, 'com.sux.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png\",\"code\":200}', 0, NULL, '2025-07-11 21:25:30', 244);
INSERT INTO `sys_oper_log` VALUES (157, '用户头像', 2, 'com.sux.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/gitData/work/文件/avatar/2025/07/11/阿摩司公爵_20250711222932A001.png\",\"code\":200}', 0, NULL, '2025-07-11 22:29:32', 399);
INSERT INTO `sys_oper_log` VALUES (158, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createId\":1,\"icon\":\"wechat\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"聊天\",\"menuType\":\"M\",\"orderNum\":1,\"params\":{},\"parentId\":0,\"path\":\"wechat\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-12 13:49:37', 22);
INSERT INTO `sys_oper_log` VALUES (159, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"system\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1,\"menuName\":\"系统管理\",\"menuType\":\"M\",\"orderNum\":2,\"params\":{},\"parentId\":0,\"path\":\"system\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-12 13:49:45', 10);
INSERT INTO `sys_oper_log` VALUES (160, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"1\",\"createId\":1,\"icon\":\"wechat\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"私聊\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2002,\"path\":\"1\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-12 13:51:19', 16);
INSERT INTO `sys_oper_log` VALUES (161, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"\",\"createTime\":\"2025-07-12 13:51:19\",\"icon\":\"wechat\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2003,\"menuName\":\"私聊\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2002,\"path\":\"privateChat\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-12 13:52:58', 13);
INSERT INTO `sys_oper_log` VALUES (162, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/chat/private-chat/index\",\"createTime\":\"2025-07-12 13:51:19\",\"icon\":\"wechat\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2003,\"menuName\":\"私聊\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2002,\"path\":\"privateChat\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-12 13:53:32', 9);
INSERT INTO `sys_oper_log` VALUES (163, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"chat/private-chat/index\",\"createTime\":\"2025-07-12 13:51:19\",\"icon\":\"wechat\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2003,\"menuName\":\"私聊\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2002,\"path\":\"privateChat\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-12 13:54:18', 11);
INSERT INTO `sys_oper_log` VALUES (164, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/2001', '127.0.0.1', '内网IP', '2001', '{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}', 0, NULL, '2025-07-22 23:55:35', 23);
INSERT INTO `sys_oper_log` VALUES (165, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3002', '127.0.0.1', '内网IP', '3002', '{\"msg\":\"存在子菜单,不允许删除\",\"code\":601}', 0, NULL, '2025-07-22 23:56:29', 5);
INSERT INTO `sys_oper_log` VALUES (166, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3020', '127.0.0.1', '内网IP', '3020', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:33', 24);
INSERT INTO `sys_oper_log` VALUES (167, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3021', '127.0.0.1', '内网IP', '3021', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:35', 17);
INSERT INTO `sys_oper_log` VALUES (168, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3022', '127.0.0.1', '内网IP', '3022', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:37', 19);
INSERT INTO `sys_oper_log` VALUES (169, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3023', '127.0.0.1', '内网IP', '3023', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:38', 17);
INSERT INTO `sys_oper_log` VALUES (170, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3024', '127.0.0.1', '内网IP', '3024', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:40', 17);
INSERT INTO `sys_oper_log` VALUES (171, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3025', '127.0.0.1', '内网IP', '3025', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:41', 19);
INSERT INTO `sys_oper_log` VALUES (172, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3026', '127.0.0.1', '内网IP', '3026', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:43', 20);
INSERT INTO `sys_oper_log` VALUES (173, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3027', '127.0.0.1', '内网IP', '3027', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:44', 12);
INSERT INTO `sys_oper_log` VALUES (174, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3028', '127.0.0.1', '内网IP', '3028', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:45', 13);
INSERT INTO `sys_oper_log` VALUES (175, '角色管理', 3, 'com.sux.web.controller.system.SysRoleController.remove()', 'DELETE', 1, 'admin', NULL, '/system/role/2', '127.0.0.1', '内网IP', '[2]', NULL, 1, '普通角色已分配,不能删除', '2025-07-22 23:57:53', 35);
INSERT INTO `sys_oper_log` VALUES (176, '角色管理', 4, 'com.sux.web.controller.system.SysRoleController.cancelAuthUserAll()', 'PUT', 1, 'admin', NULL, '/system/role/authUser/cancelAll', '127.0.0.1', '内网IP', '{\"roleId\":\"2\",\"userIds\":\"2,1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:58:01', 25);
INSERT INTO `sys_oper_log` VALUES (177, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/2001', '127.0.0.1', '内网IP', '2001', '{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}', 0, NULL, '2025-07-22 23:58:10', 6);
INSERT INTO `sys_oper_log` VALUES (178, '角色管理', 3, 'com.sux.web.controller.system.SysRoleController.remove()', 'DELETE', 1, 'admin', NULL, '/system/role/2', '127.0.0.1', '内网IP', '[2]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:58:15', 35);
INSERT INTO `sys_oper_log` VALUES (179, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/2001', '127.0.0.1', '内网IP', '2001', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:58:19', 15);
INSERT INTO `sys_oper_log` VALUES (180, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/2000', '127.0.0.1', '内网IP', '2000', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:58:21', 24);
INSERT INTO `sys_oper_log` VALUES (181, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/4000', '127.0.0.1', '内网IP', '4000', '{\"msg\":\"存在子菜单,不允许删除\",\"code\":601}', 0, NULL, '2025-07-22 23:58:26', 5);
INSERT INTO `sys_oper_log` VALUES (182, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/job/index\",\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"job\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":110,\"menuName\":\"定时任务\",\"menuType\":\"C\",\"orderNum\":10,\"params\":{},\"parentId\":1,\"path\":\"job\",\"perms\":\"monitor:job:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:58:37', 42);
INSERT INTO `sys_oper_log` VALUES (183, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/dict/index\",\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"dict\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":105,\"menuName\":\"字典管理\",\"menuType\":\"C\",\"orderNum\":6,\"params\":{},\"parentId\":1,\"path\":\"dict\",\"perms\":\"system:dict:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:58:41', 14);
INSERT INTO `sys_oper_log` VALUES (184, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/post/index\",\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"post\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":104,\"menuName\":\"岗位管理\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":1,\"path\":\"post\",\"perms\":\"system:post:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:58:45', 15);
INSERT INTO `sys_oper_log` VALUES (185, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/dept/index\",\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"tree\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":103,\"menuName\":\"部门管理\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":1,\"path\":\"dept\",\"perms\":\"system:dept:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:58:52', 15);
INSERT INTO `sys_oper_log` VALUES (186, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"system\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1,\"menuName\":\"系统管理\",\"menuType\":\"M\",\"orderNum\":99,\"params\":{},\"parentId\":0,\"path\":\"system\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:59:02', 14);
INSERT INTO `sys_oper_log` VALUES (187, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2025-07-21 00:00:00\",\"icon\":\"documentation\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3000,\"menuName\":\"企业就业服务\",\"menuType\":\"M\",\"orderNum\":1,\"params\":{},\"parentId\":0,\"path\":\"policy\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:59:20', 15);
INSERT INTO `sys_oper_log` VALUES (188, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2025-07-21 00:00:00\",\"icon\":\"documentation\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3000,\"menuName\":\"企业就业服务\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":0,\"path\":\"policy\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:59:33', 15);
INSERT INTO `sys_oper_log` VALUES (189, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"policy/policyPlan/index\",\"createTime\":\"2025-07-21 00:00:00\",\"icon\":\"edit\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3002,\"menuName\":\"政策审核\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":3000,\"path\":\"application\",\"perms\":\"policy:application:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:59:52', 12);
INSERT INTO `sys_oper_log` VALUES (190, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createId\":1,\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"初审\",\"menuType\":\"F\",\"orderNum\":0,\"params\":{},\"parentId\":3002,\"perms\":\"policy:application:first-review\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:00:33', 12);
INSERT INTO `sys_oper_log` VALUES (191, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createId\":1,\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"终审\",\"menuType\":\"F\",\"orderNum\":0,\"params\":{},\"parentId\":3002,\"perms\":\"policy:application:final-review\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:00:44', 9);
INSERT INTO `sys_oper_log` VALUES (192, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"policy/info/index\",\"createTime\":\"2025-07-21 00:00:00\",\"icon\":\"form\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3001,\"menuName\":\"服务信息\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":3000,\"path\":\"info\",\"perms\":\"policy:info:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:00:52', 11);
INSERT INTO `sys_oper_log` VALUES (193, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"policy/policyPlan/index\",\"createTime\":\"2025-07-21 00:00:00\",\"icon\":\"edit\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3002,\"menuName\":\"服务审核\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":3000,\"path\":\"application\",\"perms\":\"policy:application:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:00:57', 14);
INSERT INTO `sys_oper_log` VALUES (194, '角色管理', 1, 'com.sux.web.controller.system.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"dataScope\":\"1\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[3000,3002,4046],\"params\":{},\"roleId\":100,\"roleKey\":\"初审\",\"roleName\":\"企业就业服务初审\",\"roleSort\":0,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:01:37', 40);
INSERT INTO `sys_oper_log` VALUES (195, '角色管理', 1, 'com.sux.web.controller.system.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"dataScope\":\"1\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[3000,3002,4045],\"params\":{},\"roleId\":101,\"roleKey\":\"终审\",\"roleName\":\"企业就业服务终审\",\"roleSort\":0,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:01:59', 11);
INSERT INTO `sys_oper_log` VALUES (196, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-23 00:01:59\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[3000,3002,4046],\"params\":{},\"roleId\":101,\"roleKey\":\"终审\",\"roleName\":\"企业就业服务终审\",\"roleSort\":0,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:02:16', 24);
INSERT INTO `sys_oper_log` VALUES (197, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"policy/policyS/index\",\"createId\":1,\"icon\":\"button\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"企业就业服务申请\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":0,\"path\":\"policyS\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:02:55', 10);
INSERT INTO `sys_oper_log` VALUES (198, '角色管理', 1, 'com.sux.web.controller.system.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"dataScope\":\"1\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[4047],\"params\":{},\"roleId\":102,\"roleKey\":\"企业就业\",\"roleName\":\"企业就业服务申请\",\"roleSort\":0,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:03:18', 15);
INSERT INTO `sys_oper_log` VALUES (199, '用户管理', 1, 'com.sux.web.controller.system.SysUserController.add()', 'POST', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"email\":\"\",\"nickName\":\"admin_1\",\"params\":{},\"postIds\":[],\"roleIds\":[],\"status\":\"0\",\"userId\":100,\"userName\":\"admin_1\",\"userType\":\"00\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:03:41', 164);
INSERT INTO `sys_oper_log` VALUES (200, '用户管理', 2, 'com.sux.web.controller.system.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"avatar\":\"\",\"createId\":1,\"createTime\":\"2025-07-23 00:03:41\",\"delFlag\":\"0\",\"email\":\"\",\"loginIp\":\"\",\"nickName\":\"admin_1\",\"params\":{},\"phonenumber\":\"\",\"postIds\":[],\"roleIds\":[100],\"roles\":[],\"sex\":\"0\",\"status\":\"0\",\"updateId\":1,\"userId\":100,\"userName\":\"admin_1\",\"userType\":\"00\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:03:45', 27);
INSERT INTO `sys_oper_log` VALUES (201, '用户管理', 1, 'com.sux.web.controller.system.SysUserController.add()', 'POST', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"email\":\"\",\"nickName\":\"admin_2\",\"params\":{},\"postIds\":[],\"roleIds\":[101],\"status\":\"0\",\"userId\":101,\"userName\":\"admin_2\",\"userType\":\"00\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:03:55', 126);
INSERT INTO `sys_oper_log` VALUES (202, '用户管理', 1, 'com.sux.web.controller.system.SysUserController.add()', 'POST', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"email\":\"\",\"nickName\":\"admin_3\",\"params\":{},\"postIds\":[],\"roleIds\":[102],\"status\":\"0\",\"userId\":102,\"userName\":\"admin_3\",\"userType\":\"00\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:04:03', 149);
INSERT INTO `sys_oper_log` VALUES (203, '个人信息', 2, 'com.sux.web.controller.system.SysProfileController.updatePwd()', 'PUT', 1, 'admin_3', NULL, '/system/user/profile/updatePwd', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:04:28', 506);
INSERT INTO `sys_oper_log` VALUES (204, '政策申请', 1, 'com.sux.web.controller.policy.PolicyApplicationController.add()', 'POST', 1, 'admin_3', NULL, '/policy/application', '127.0.0.1', '内网IP', '{\"applicantName\":\"张三\",\"applicantPhone\":\"***********\",\"applicantUserId\":102,\"applicationId\":2,\"applicationStatus\":\"0\",\"createId\":102,\"createTime\":\"2025-07-23 00:05:14\",\"params\":{},\"policyId\":1,\"remark\":\"\",\"requiredMaterials\":\"[{\\\"name\\\":\\\"《企业新增岗位吸纳就业困难人员、高校毕业生和退役军人社保补贴申领表》\\\",\\\"required\\\":true,\\\"files\\\":[{\\\"name\\\":\\\"【哲风壁纸】二次元场景-动漫插画_20250723000455A001.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】二次元场景-动漫插画.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】二次元场景-动漫插画_20250723000455A001.png\\\",\\\"uid\\\":*************,\\\"status\\\":\\\"success\\\"}]},{\\\"name\\\":\\\"企业的营业执照副本复印件\\\",\\\"required\\\":true,\\\"files\\\":[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723000457A002.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723000457A002.png\\\",\\\"uid\\\":*************,\\\"status\\\":\\\"success\\\"}]},{\\\"name\\\":\\\"退役军人需提供退伍证原件及复印件\\\",\\\"required\\\":false,\\\"files\\\":[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723000459A003.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723000459A003.png\\\",\\\"uid\\\":1753200299883,\\\"status\\\":\\\"success\\\"}]},{\\\"name\\\":\\\"企业社保缴费凭证\\\",\\\"required\\\":true,\\\"files\\\":[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723000502A004.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723000502A004.png\\\",\\\"uid\\\":1753200302121,\\\"status\\\":\\\"success\\\"}]},{\\\"name\\\":\\\"企业在银行开立的基本账户\\\",\\\"required\\\":true,\\\"files\\\":[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723000503A005.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723000503A005.png\\\",\\\"uid\\\":1753200303955,\\\"status\\\":\\\"success\\\"}]}]\",\"submitTime\":\"2025-07-23 00:05:14\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:05:14', 33);
INSERT INTO `sys_oper_log` VALUES (205, '个人信息', 2, 'com.sux.web.controller.system.SysProfileController.updatePwd()', 'PUT', 1, 'admin_1', NULL, '/system/user/profile/updatePwd', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:05:48', 487);
INSERT INTO `sys_oper_log` VALUES (206, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"policy/policyS/index\",\"createTime\":\"2025-07-23 00:02:55\",\"icon\":\"button\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":4047,\"menuName\":\"企业就业服务申请\",\"menuType\":\"M\",\"orderNum\":1,\"params\":{},\"parentId\":0,\"path\":\"policyS\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:06:17', 15);
INSERT INTO `sys_oper_log` VALUES (207, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2025-07-21 00:00:00\",\"icon\":\"documentation\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3000,\"menuName\":\"企业就业服务\",\"menuType\":\"M\",\"orderNum\":1,\"params\":{},\"parentId\":0,\"path\":\"policy\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:06:32', 9);
INSERT INTO `sys_oper_log` VALUES (208, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-23 00:01:59\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[3000,3002,3001,3010,3011,3012,3013,3014,4046],\"params\":{},\"roleId\":101,\"roleKey\":\"终审\",\"roleName\":\"企业就业服务终审\",\"roleSort\":0,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:06:55', 19);
INSERT INTO `sys_oper_log` VALUES (209, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-23 00:01:37\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[3000,3002,3001,3010,3011,3012,3013,3014,4046],\"params\":{},\"roleId\":100,\"roleKey\":\"初审\",\"roleName\":\"企业就业服务初审\",\"roleSort\":0,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:07:04', 21);
INSERT INTO `sys_oper_log` VALUES (210, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"policy/info/index\",\"createTime\":\"2025-07-21 00:00:00\",\"icon\":\"form\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3001,\"menuName\":\"服务信息\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":3000,\"path\":\"policyinfo\",\"perms\":\"policy:info:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:07:29', 11);
INSERT INTO `sys_oper_log` VALUES (211, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"policy/policy/index\",\"createTime\":\"2025-07-21 00:00:00\",\"icon\":\"form\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3001,\"menuName\":\"服务信息\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":3000,\"path\":\"policy\",\"perms\":\"policy:info:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:07:44', 16);
INSERT INTO `sys_oper_log` VALUES (212, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-23 00:01:37\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[3000,3002,3001,3010,3011,3012,3013,3014,4045],\"params\":{},\"roleId\":100,\"roleKey\":\"初审\",\"roleName\":\"企业就业服务初审\",\"roleSort\":0,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:08:40', 21);
INSERT INTO `sys_oper_log` VALUES (213, '政策申请初审', 2, 'com.sux.web.controller.policy.PolicyApplicationController.firstReview()', 'POST', 1, 'admin_1', NULL, '/policy/application/first-review', '127.0.0.1', '内网IP', '{\"applicationId\":1,\"approvalStatus\":\"1\",\"approvalComment\":\"ces \"}', '{\"msg\":\"初审通过成功\",\"code\":200}', 0, NULL, '2025-07-23 00:08:53', 20);
INSERT INTO `sys_oper_log` VALUES (214, '个人信息', 2, 'com.sux.web.controller.system.SysProfileController.updatePwd()', 'PUT', 1, 'admin_2', NULL, '/system/user/profile/updatePwd', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:09:15', 463);
INSERT INTO `sys_oper_log` VALUES (215, '政策申请终审', 2, 'com.sux.web.controller.policy.PolicyApplicationController.finalReview()', 'POST', 1, 'admin_2', NULL, '/policy/application/final-review', '127.0.0.1', '内网IP', '{\"applicationId\":1,\"approvalStatus\":\"2\",\"approvalComment\":\"ceshi \"}', '{\"msg\":\"终审拒绝成功\",\"code\":200}', 0, NULL, '2025-07-23 00:09:25', 12);
INSERT INTO `sys_oper_log` VALUES (216, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createId\":1,\"icon\":\"clipboard\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"就业培训管理\",\"menuType\":\"M\",\"orderNum\":0,\"params\":{},\"parentId\":0,\"path\":\"order\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:11:03', 9);
INSERT INTO `sys_oper_log` VALUES (217, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/index\",\"createTime\":\"2025-07-22 00:00:00\",\"icon\":\"skill\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3003,\"menuName\":\"培训订单\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":4048,\"path\":\"order\",\"perms\":\"training:order:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:11:09', 13);
INSERT INTO `sys_oper_log` VALUES (218, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2025-07-23 00:11:03\",\"icon\":\"clipboard\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":4048,\"menuName\":\"就业培训管理\",\"menuType\":\"M\",\"orderNum\":2,\"params\":{},\"parentId\":0,\"path\":\"order\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:11:13', 9);
INSERT INTO `sys_oper_log` VALUES (219, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"policy/policyPlan/index\",\"createTime\":\"2025-07-21 00:00:00\",\"icon\":\"edit\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3002,\"menuName\":\"服务审核\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":3000,\"path\":\"policyPlan\",\"perms\":\"policy:application:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 14:09:13', 16);
INSERT INTO `sys_oper_log` VALUES (220, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"1\",\"createId\":1,\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"个人培训申请\",\"menuType\":\"C\",\"orderNum\":0,\"params\":{},\"parentId\":0,\"path\":\"personApplicon\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 14:14:09', 6);
INSERT INTO `sys_oper_log` VALUES (221, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"1\",\"createTime\":\"2025-07-23 14:14:09\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5059,\"menuName\":\"个人培训申请\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"personApplicon\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 14:14:18', 14);
INSERT INTO `sys_oper_log` VALUES (222, '角色管理', 1, 'com.sux.web.controller.system.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"dataScope\":\"1\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[4048,3003,3030,3031,3032,3033,3034,3035,3036],\"params\":{},\"roleId\":103,\"roleKey\":\"企业就业培训管理\",\"roleName\":\"企业就业培训管理\",\"roleSort\":10,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 14:15:17', 23);
INSERT INTO `sys_oper_log` VALUES (223, '角色管理', 1, 'com.sux.web.controller.system.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"dataScope\":\"1\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[5059],\"params\":{},\"roleId\":104,\"roleKey\":\"个人培训申请\",\"roleName\":\"个人培训申请\",\"roleSort\":0,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 14:15:42', 12);
INSERT INTO `sys_oper_log` VALUES (224, '角色管理', 1, 'com.sux.web.controller.system.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"dataScope\":\"1\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[],\"params\":{},\"roleId\":105,\"roleKey\":\"企业培训申请\",\"roleName\":\"企业培训申请\",\"roleSort\":11,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 14:15:59', 13);
INSERT INTO `sys_oper_log` VALUES (225, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-23 14:15:42\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[5059],\"params\":{},\"roleId\":104,\"roleKey\":\"个人培训申请\",\"roleName\":\"个人培训申请\",\"roleSort\":12,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 14:16:06', 15);
INSERT INTO `sys_oper_log` VALUES (226, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/index\",\"createTime\":\"2025-07-22 00:00:00\",\"icon\":\"skill\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3003,\"menuName\":\"培训报名管理\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":4048,\"path\":\"order\",\"perms\":\"training:order:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 14:19:47', 10);
INSERT INTO `sys_oper_log` VALUES (227, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"zhaop/application/index\",\"createTime\":\"2025-07-23 14:14:09\",\"icon\":\"edit\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5059,\"menuName\":\"个人培训申请\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"personApplicon\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 15:59:50', 17);
INSERT INTO `sys_oper_log` VALUES (228, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"zhaop/application/signup\",\"createTime\":\"2025-07-23 14:14:09\",\"icon\":\"edit\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5059,\"menuName\":\"个人培训申请\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"personApplicon\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 16:54:01', 42);
INSERT INTO `sys_oper_log` VALUES (229, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"zhaop/application/index\",\"createId\":1,\"icon\":\"date\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"个人培训报名信息\",\"menuType\":\"C\",\"orderNum\":0,\"params\":{},\"parentId\":4048,\"path\":\"personappIndex\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 16:54:37', 7);
INSERT INTO `sys_oper_log` VALUES (230, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"zhaop/application/index\",\"createTime\":\"2025-07-23 16:54:37\",\"icon\":\"date\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5068,\"menuName\":\"个人培训报名信息\",\"menuType\":\"C\",\"orderNum\":0,\"params\":{},\"parentId\":4048,\"path\":\"personappIndexzhap\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 17:05:33', 10);
INSERT INTO `sys_oper_log` VALUES (231, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/5068', '127.0.0.1', '内网IP', '5068', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 18:35:56', 38);
INSERT INTO `sys_oper_log` VALUES (232, '培训报名', 1, 'com.sux.web.controller.training.PublicTrainingApplicationController.submitApplication()', 'POST', 1, 'admin', NULL, '/public/training/application/submit', '127.0.0.1', '内网IP', '{\"applicantAddress\":\"\",\"applicantEducation\":\"\",\"applicantEmail\":\"\",\"applicantExperience\":\"\",\"applicantGender\":\"\",\"applicantIdCard\":\"\",\"applicantName\":\"你好\",\"applicantPhone\":\"15454545454\",\"applicationId\":4,\"applicationNote\":\"\",\"applicationStatus\":\"0\",\"applicationTime\":\"2025-07-23 18:36:28\",\"createId\":1,\"createTime\":\"2025-07-23 18:36:28\",\"orderId\":8,\"params\":{},\"userId\":1}', '{\"msg\":\"报名成功，请等待审核\",\"code\":200}', 0, NULL, '2025-07-23 18:36:28', 113);
INSERT INTO `sys_oper_log` VALUES (233, '培训报名', 2, 'com.sux.web.controller.training.PublicTrainingApplicationController.cancelMyApplication()', 'PUT', 1, 'admin', NULL, '/public/training/application/cancel/4', '127.0.0.1', '内网IP', '4', '{\"msg\":\"取消报名成功\",\"code\":200}', 0, NULL, '2025-07-23 18:36:32', 38);
INSERT INTO `sys_oper_log` VALUES (234, '培训报名', 1, 'com.sux.web.controller.training.PublicTrainingApplicationController.submitApplication()', 'POST', 1, 'admin', NULL, '/public/training/application/submit', '127.0.0.1', '内网IP', '{\"applicantAddress\":\"\",\"applicantEducation\":\"\",\"applicantEmail\":\"\",\"applicantExperience\":\"\",\"applicantGender\":\"\",\"applicantIdCard\":\"\",\"applicantName\":\"你好\",\"applicantPhone\":\"15454545454\",\"applicationId\":5,\"applicationNote\":\"\",\"applicationStatus\":\"0\",\"applicationTime\":\"2025-07-23 18:36:36\",\"createId\":1,\"createTime\":\"2025-07-23 18:36:36\",\"orderId\":8,\"params\":{},\"userId\":1}', '{\"msg\":\"报名成功，请等待审核\",\"code\":200}', 0, NULL, '2025-07-23 18:36:36', 32);
INSERT INTO `sys_oper_log` VALUES (235, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/application/index\",\"createTime\":\"2025-07-23 16:36:51\",\"icon\":\"form\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5060,\"menuName\":\"培训报名管理\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":2000,\"path\":\"application\",\"perms\":\"training:application:list\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 18:38:02', 34);
INSERT INTO `sys_oper_log` VALUES (236, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/application/signup\",\"createTime\":\"2025-07-23 14:14:09\",\"icon\":\"edit\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5059,\"menuName\":\"个人培训申请\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"personApplicon\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 18:38:13', 20);
INSERT INTO `sys_oper_log` VALUES (237, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/application/index\",\"createTime\":\"2025-07-23 16:36:51\",\"icon\":\"form\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5060,\"menuName\":\"培训报名管理\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":4048,\"path\":\"application\",\"perms\":\"training:application:list\",\"routeName\":\"\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"修改菜单\'培训报名管理\'失败，菜单名称已存在\",\"code\":500}', 0, NULL, '2025-07-23 18:42:21', 7);
INSERT INTO `sys_oper_log` VALUES (238, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/application/index\",\"createTime\":\"2025-07-23 16:36:51\",\"icon\":\"form\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5060,\"menuName\":\"培训报名\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":4048,\"path\":\"application\",\"perms\":\"training:application:list\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 18:42:27', 14);
INSERT INTO `sys_oper_log` VALUES (239, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/application/index\",\"createTime\":\"2025-07-23 16:36:51\",\"icon\":\"form\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5060,\"menuName\":\"个人培训报名\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":4048,\"path\":\"application\",\"perms\":\"training:application:list\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 18:43:09', 15);
INSERT INTO `sys_oper_log` VALUES (240, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/index\",\"createTime\":\"2025-07-22 00:00:00\",\"icon\":\"skill\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3003,\"menuName\":\"个人培训报名管理\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":4048,\"path\":\"order\",\"perms\":\"training:order:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 18:43:15', 14);
INSERT INTO `sys_oper_log` VALUES (241, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/application/index\",\"createTime\":\"2025-07-23 16:36:51\",\"icon\":\"form\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5060,\"menuName\":\"个人培训报名记录\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":4048,\"path\":\"application\",\"perms\":\"training:application:list\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 18:43:25', 14);
INSERT INTO `sys_oper_log` VALUES (242, '培训报名', 2, 'com.sux.web.controller.training.TrainingApplicationController.review()', 'PUT', 1, 'admin', NULL, '/training/application/review/5', '127.0.0.1', '内网IP', '{\"reviewComment\":\"测试\",\"status\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 18:43:48', 42);
INSERT INTO `sys_oper_log` VALUES (243, '用户管理', 1, 'com.sux.web.controller.system.SysUserController.add()', 'POST', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"email\":\"\",\"nickName\":\"企业就业培训申请\",\"params\":{},\"phonenumber\":\"\",\"postIds\":[],\"roleIds\":[],\"status\":\"0\",\"userId\":103,\"userName\":\"企业就业培训申请\",\"userType\":\"01\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 18:46:54', 230);
INSERT INTO `sys_oper_log` VALUES (244, '个人信息', 2, 'com.sux.web.controller.system.SysProfileController.updatePwd()', 'PUT', 1, '企业就业培训申请', NULL, '/system/user/profile/updatePwd', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 18:47:23', 530);
INSERT INTO `sys_oper_log` VALUES (245, '用户管理', 2, 'com.sux.web.controller.system.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"avatar\":\"\",\"createId\":1,\"createTime\":\"2025-07-23 18:46:54\",\"delFlag\":\"0\",\"email\":\"\",\"loginDate\":\"2025-07-23 18:47:09\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"企业就业培训申请\",\"params\":{},\"phonenumber\":\"\",\"postIds\":[],\"pwdUpdateDate\":\"2025-07-23 18:47:23\",\"roleIds\":[104],\"roles\":[],\"sex\":\"0\",\"status\":\"0\",\"updateId\":1,\"userId\":103,\"userName\":\"企业就业培训申请\",\"userType\":\"00\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 18:47:36', 27);
INSERT INTO `sys_oper_log` VALUES (246, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/application/signup\",\"createTime\":\"2025-07-23 14:14:09\",\"icon\":\"edit\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5059,\"menuName\":\"个人培训申请\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"order/application/signup\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 19:09:48', 16);
INSERT INTO `sys_oper_log` VALUES (247, '培训报名', 1, 'com.sux.web.controller.training.PublicTrainingApplicationController.submitApplication()', 'POST', 1, 'admin', NULL, '/public/training/application/submit', '127.0.0.1', '内网IP', '{\"applicantAddress\":\"\",\"applicantEducation\":\"小学\",\"applicantEmail\":\"\",\"applicantExperience\":\"\",\"applicantGender\":\"\",\"applicantIdCard\":\"\",\"applicantName\":\"12321\",\"applicantPhone\":\"17574857483\",\"applicationId\":6,\"applicationNote\":\"\",\"applicationStatus\":\"0\",\"applicationTime\":\"2025-07-23 19:24:52\",\"createId\":1,\"createTime\":\"2025-07-23 19:24:52\",\"orderId\":7,\"params\":{},\"userId\":1}', '{\"msg\":\"报名成功，请等待审核\",\"code\":200}', 0, NULL, '2025-07-23 19:24:52', 10);
INSERT INTO `sys_oper_log` VALUES (248, '培训报名', 2, 'com.sux.web.controller.training.PublicTrainingApplicationController.cancelMyApplication()', 'PUT', 1, 'admin', NULL, '/public/training/application/cancel/6', '127.0.0.1', '内网IP', '6', '{\"msg\":\"取消报名成功\",\"code\":200}', 0, NULL, '2025-07-23 19:24:57', 11);
INSERT INTO `sys_oper_log` VALUES (249, '培训报名', 1, 'com.sux.web.controller.training.PublicTrainingApplicationController.submitApplication()', 'POST', 1, 'admin', NULL, '/public/training/application/submit', '127.0.0.1', '内网IP', '{\"applicantAddress\":\"\",\"applicantEducation\":\"小学\",\"applicantEmail\":\"\",\"applicantExperience\":\"\",\"applicantGender\":\"\",\"applicantIdCard\":\"\",\"applicantName\":\"12321\",\"applicantPhone\":\"17574857483\",\"applicationId\":7,\"applicationNote\":\"\",\"applicationStatus\":\"0\",\"applicationTime\":\"2025-07-23 19:24:58\",\"createId\":1,\"createTime\":\"2025-07-23 19:24:58\",\"orderId\":7,\"params\":{},\"userId\":1}', '{\"msg\":\"报名成功，请等待审核\",\"code\":200}', 0, NULL, '2025-07-23 19:24:58', 11);
INSERT INTO `sys_oper_log` VALUES (250, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createId\":1,\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"机构培训申请\",\"menuType\":\"C\",\"orderNum\":0,\"params\":{},\"parentId\":0,\"path\":\"1\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 21:57:14', 47);
INSERT INTO `sys_oper_log` VALUES (251, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2025-07-22 00:00:00\",\"icon\":\"peoples\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5000,\"menuName\":\"招聘管理\",\"menuType\":\"M\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"job\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 21:57:26', 19);
INSERT INTO `sys_oper_log` VALUES (252, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/application/institution-apply\",\"createTime\":\"2025-07-23 21:57:14\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5069,\"menuName\":\"机构培训申请\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"institution-apply\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 21:57:58', 19);
INSERT INTO `sys_oper_log` VALUES (253, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/application/institution-apply\",\"createTime\":\"2025-07-23 21:57:14\",\"icon\":\"component\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5069,\"menuName\":\"机构培训申请\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"institution-apply\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 21:58:25', 17);
INSERT INTO `sys_oper_log` VALUES (254, '培训机构申请', 1, 'com.sux.web.controller.training.PublicTrainingInstitutionApplicationController.submitApplication()', 'POST', 1, 'admin', NULL, '/public/training/institution/application/submit', '127.0.0.1', '内网IP', '{\"applicationId\":3,\"applicationNote\":\"\",\"applicationStatus\":\"0\",\"applicationTime\":\"2025-07-23 22:08:17\",\"businessScope\":\"\",\"contactEmail\":\"<EMAIL>\",\"contactPerson\":\"11\",\"contactPhone\":\"***********\",\"createId\":1,\"createTime\":\"2025-07-23 22:08:17\",\"establishedDate\":\"2025-07-01\",\"facilityInfo\":\"测试\",\"institutionAddress\":\"电商3421412\",\"institutionCode\":\"\",\"institutionName\":\"深圳灭换家机构\",\"institutionType\":\"\",\"legalPerson\":\"李某\",\"orderId\":8,\"params\":{},\"teacherInfo\":\"测试\",\"trainingCapacity\":\"测试\",\"trainingExperience\":\"测试\",\"trainingPlan\":\"测试\",\"userId\":1}', '{\"msg\":\"申请提交成功，请等待审核\",\"code\":200}', 0, NULL, '2025-07-23 22:08:18', 455);
INSERT INTO `sys_oper_log` VALUES (255, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/application/institution-management\",\"createTime\":\"2025-07-23 22:08:50\",\"icon\":\"peoples\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5070,\"menuName\":\"培训机构申请管理\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":4048,\"path\":\"institution-application\",\"perms\":\"training:institution:application:list\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:09:10', 58);
INSERT INTO `sys_oper_log` VALUES (256, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/index\",\"createTime\":\"2025-07-22 00:00:00\",\"icon\":\"skill\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3003,\"menuName\":\"培训管理\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":4048,\"path\":\"order\",\"perms\":\"training:order:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:09:17', 21);
INSERT INTO `sys_oper_log` VALUES (257, '培训机构申请审核', 2, 'com.sux.web.controller.training.TrainingInstitutionApplicationController.review()', 'PUT', 1, 'admin', NULL, '/training/institution/application/review/3', '127.0.0.1', '内网IP', '{\"reviewComment\":\"测试\",\"status\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:09:27', 32);
INSERT INTO `sys_oper_log` VALUES (258, '培训机构申请审核', 2, 'com.sux.web.controller.training.TrainingInstitutionApplicationController.review()', 'PUT', 1, 'admin', NULL, '/training/institution/application/review/2', '127.0.0.1', '内网IP', '{\"reviewComment\":\"测试\",\"status\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:09:29', 17);
INSERT INTO `sys_oper_log` VALUES (259, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/application/institution-apply\",\"createTime\":\"2025-07-23 21:57:14\",\"icon\":\"component\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5069,\"menuName\":\"机构培训申请\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"order/application/institution-apply\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:11:13', 21);
INSERT INTO `sys_oper_log` VALUES (260, '零工信息', 1, 'com.sux.web.controller.job.WorkerProfileController.add()', 'POST', 1, 'admin', NULL, '/job/worker', '127.0.0.1', '内网IP', '{\"completedJobs\":0,\"createId\":1,\"createTime\":\"2025-07-23 22:14:41\",\"currentLocation\":\"深圳\",\"educationLevel\":\"初中\",\"gender\":\"female\",\"isVerified\":0,\"jobTypesPreferred\":\"全职\",\"lastActiveTime\":\"2025-07-23 22:14:41\",\"params\":{},\"phone\":\"16545454545\",\"ratingAverage\":0.00,\"ratingCount\":0,\"realName\":\"李某\",\"salaryExpectationMax\":49999,\"salaryExpectationMin\":1999,\"salaryTypePreference\":\"hourly\",\"status\":\"active\",\"successRate\":0.00,\"userId\":1,\"workCategories\":\"搬运工\",\"workerId\":14}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:14:41', 116);
INSERT INTO `sys_oper_log` VALUES (261, '零工信息', 2, 'com.sux.web.controller.job.WorkerProfileController.edit()', 'PUT', 1, 'admin', NULL, '/job/worker', '127.0.0.1', '内网IP', '{\"completedJobs\":0,\"createId\":1,\"createTime\":\"2025-07-23 22:14:41\",\"currentLocation\":\"深圳\",\"delFlag\":\"0\",\"educationLevel\":\"初中\",\"gender\":\"female\",\"isVerified\":0,\"jobTypesPreferred\":\"全职\",\"lastActiveTime\":\"2025-07-23 22:14:41\",\"params\":{},\"phone\":\"16545454545\",\"ratingAverage\":0,\"ratingCount\":0,\"realName\":\"李某\",\"salaryExpectationMax\":49999,\"salaryExpectationMin\":1999,\"salaryTypePreference\":\"hourly\",\"status\":\"active\",\"successRate\":0,\"updateId\":1,\"updateTime\":\"2025-07-23 22:14:45\",\"userId\":1,\"userName\":\"admin\",\"workCategories\":\"搬运工\",\"workerId\":14}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:14:45', 17);
INSERT INTO `sys_oper_log` VALUES (262, '零工信息', 2, 'com.sux.web.controller.job.WorkerProfileController.verify()', 'PUT', 1, 'admin', NULL, '/job/worker/verify/14', '127.0.0.1', '内网IP', '14', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:14:48', 7);
INSERT INTO `sys_oper_log` VALUES (263, '零工信息', 2, 'com.sux.web.controller.job.WorkerProfileController.suspend()', 'PUT', 1, 'admin', NULL, '/job/worker/suspend/14', '127.0.0.1', '内网IP', '14', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:14:50', 7);
INSERT INTO `sys_oper_log` VALUES (264, '零工信息', 2, 'com.sux.web.controller.job.WorkerProfileController.activate()', 'PUT', 1, 'admin', NULL, '/job/worker/activate/14', '127.0.0.1', '内网IP', '14', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:14:51', 7);
INSERT INTO `sys_oper_log` VALUES (265, '招聘信息', 1, 'com.sux.web.controller.job.JobPostingController.add()', 'POST', 1, 'admin', NULL, '/job/posting', '127.0.0.1', '内网IP', '{\"contactPerson\":\"14\",\"contactPhone\":\"14456767676\",\"educationRequired\":\"初中\",\"jobCategory\":\"销售\",\"jobDescription\":\"超市\",\"jobTitle\":\"超市\",\"jobType\":\"全职\",\"params\":{},\"positionsAvailable\":10,\"salaryMax\":100000,\"salaryMin\":10000,\"salaryType\":\"monthly\",\"status\":\"draft\",\"workLocation\":\"北京\"}', NULL, 1, '\r\n### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column \'search_value\' in \'field list\'\r\n### The error may exist in com/sux/system/mapper/JobPostingMapper.java (best guess)\r\n### The error may involve defaultParameterMap\r\n### The error occurred while setting parameters\r\n### SQL: SELECT  job_id,job_title,job_description,job_type,job_category,work_location,salary_type,salary_min,salary_max,education_required,experience_required,work_hours_per_day,work_days_per_week,start_date,end_date,contact_person,contact_phone,company_name,urgency_level,positions_available,positions_filled,status,view_count,application_count,publisher_user_id,is_verified,featured,del_flag,search_value,create_id,create_time,update_id,update_time,remark,params  FROM job_posting      WHERE  (job_title = ? AND publisher_user_id = ? AND del_flag = ?)\r\n### Cause: java.sql.SQLSyntaxErrorException: Unknown column \'search_value\' in \'field list\'\n; bad SQL grammar []', '2025-07-23 22:16:36', 145);
INSERT INTO `sys_oper_log` VALUES (266, '招聘信息', 1, 'com.sux.web.controller.job.JobPostingController.add()', 'POST', 1, 'admin', NULL, '/job/posting', '127.0.0.1', '内网IP', '{\"contactPerson\":\"14\",\"contactPhone\":\"14456767676\",\"educationRequired\":\"初中\",\"jobCategory\":\"销售\",\"jobDescription\":\"超市\",\"jobTitle\":\"超市\",\"jobType\":\"全职\",\"params\":{},\"positionsAvailable\":10,\"salaryMax\":100000,\"salaryMin\":10000,\"salaryType\":\"monthly\",\"status\":\"draft\",\"workLocation\":\"北京\"}', NULL, 1, '\r\n### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column \'search_value\' in \'field list\'\r\n### The error may exist in com/sux/system/mapper/JobPostingMapper.java (best guess)\r\n### The error may involve defaultParameterMap\r\n### The error occurred while setting parameters\r\n### SQL: SELECT  job_id,job_title,job_description,job_type,job_category,work_location,salary_type,salary_min,salary_max,education_required,experience_required,work_hours_per_day,work_days_per_week,start_date,end_date,contact_person,contact_phone,company_name,urgency_level,positions_available,positions_filled,status,view_count,application_count,publisher_user_id,is_verified,featured,del_flag,search_value,create_id,create_time,update_id,update_time,remark,params  FROM job_posting      WHERE  (job_title = ? AND publisher_user_id = ? AND del_flag = ?)\r\n### Cause: java.sql.SQLSyntaxErrorException: Unknown column \'search_value\' in \'field list\'\n; bad SQL grammar []', '2025-07-23 22:16:40', 5);
INSERT INTO `sys_oper_log` VALUES (267, '招聘信息', 1, 'com.sux.web.controller.job.JobPostingController.add()', 'POST', 1, 'admin', NULL, '/job/posting', '127.0.0.1', '内网IP', '{\"contactPerson\":\"14\",\"contactPhone\":\"14456767676\",\"educationRequired\":\"初中\",\"jobCategory\":\"销售\",\"jobDescription\":\"超市\",\"jobTitle\":\"超市\",\"jobType\":\"全职\",\"params\":{},\"positionsAvailable\":10,\"salaryMax\":100000,\"salaryMin\":10000,\"salaryType\":\"monthly\",\"status\":\"draft\",\"workLocation\":\"北京\"}', NULL, 1, '\r\n### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column \'search_value\' in \'field list\'\r\n### The error may exist in com/sux/system/mapper/JobPostingMapper.java (best guess)\r\n### The error may involve defaultParameterMap\r\n### The error occurred while setting parameters\r\n### SQL: SELECT  job_id,job_title,job_description,job_type,job_category,work_location,salary_type,salary_min,salary_max,education_required,experience_required,work_hours_per_day,work_days_per_week,start_date,end_date,contact_person,contact_phone,company_name,urgency_level,positions_available,positions_filled,status,view_count,application_count,publisher_user_id,is_verified,featured,del_flag,search_value,create_id,create_time,update_id,update_time,remark,params  FROM job_posting      WHERE  (job_title = ? AND publisher_user_id = ? AND del_flag = ?)\r\n### Cause: java.sql.SQLSyntaxErrorException: Unknown column \'search_value\' in \'field list\'\n; bad SQL grammar []', '2025-07-23 22:21:04', 338);
INSERT INTO `sys_oper_log` VALUES (268, '招聘信息', 1, 'com.sux.web.controller.job.JobPostingController.add()', 'POST', 1, 'admin', NULL, '/job/posting', '127.0.0.1', '内网IP', '{\"applicationCount\":0,\"contactPerson\":\"14\",\"contactPhone\":\"14456767676\",\"createId\":1,\"createTime\":\"2025-07-23 22:22:05\",\"educationRequired\":\"初中\",\"featured\":0,\"isVerified\":0,\"jobCategory\":\"销售\",\"jobDescription\":\"超市\",\"jobId\":14,\"jobTitle\":\"超市\",\"jobType\":\"全职\",\"params\":{},\"positionsAvailable\":10,\"positionsFilled\":0,\"publisherUserId\":1,\"salaryMax\":100000,\"salaryMin\":10000,\"salaryType\":\"monthly\",\"status\":\"draft\",\"urgencyLevel\":\"normal\",\"viewCount\":0,\"workLocation\":\"北京\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:22:05', 105);
INSERT INTO `sys_oper_log` VALUES (269, '招聘信息', 2, 'com.sux.web.controller.job.JobPostingController.edit()', 'PUT', 1, 'admin', NULL, '/job/posting', '127.0.0.1', '内网IP', '{\"applicationCount\":0,\"contactPerson\":\"14\",\"contactPhone\":\"14456767676\",\"createId\":1,\"createTime\":\"2025-07-23 22:22:06\",\"delFlag\":\"0\",\"educationRequired\":\"初中\",\"featured\":0,\"isVerified\":0,\"jobCategory\":\"保洁\",\"jobDescription\":\"超市\",\"jobId\":14,\"jobTitle\":\"超市\",\"jobType\":\"全职\",\"params\":{},\"positionsAvailable\":10,\"positionsFilled\":0,\"publisherUserId\":1,\"publisherUserName\":\"admin\",\"salaryMax\":100000,\"salaryMin\":10000,\"salaryType\":\"monthly\",\"status\":\"draft\",\"updateId\":1,\"updateTime\":\"2025-07-23 22:22:10\",\"urgencyLevel\":\"normal\",\"viewCount\":1,\"workLocation\":\"北京\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:22:10', 18);
INSERT INTO `sys_oper_log` VALUES (270, '招聘信息', 2, 'com.sux.web.controller.job.JobPostingController.edit()', 'PUT', 1, 'admin', NULL, '/job/posting', '127.0.0.1', '内网IP', '{\"applicationCount\":0,\"contactPerson\":\"14\",\"contactPhone\":\"14456767676\",\"createId\":1,\"createTime\":\"2025-07-23 22:22:06\",\"delFlag\":\"0\",\"educationRequired\":\"初中\",\"featured\":0,\"isVerified\":0,\"jobCategory\":\"销售\",\"jobDescription\":\"超市\",\"jobId\":14,\"jobTitle\":\"超市\",\"jobType\":\"全职\",\"params\":{},\"positionsAvailable\":10,\"positionsFilled\":0,\"publisherUserId\":1,\"publisherUserName\":\"admin\",\"salaryMax\":100000,\"salaryMin\":10000,\"salaryType\":\"monthly\",\"status\":\"draft\",\"updateId\":1,\"updateTime\":\"2025-07-23 22:22:14\",\"urgencyLevel\":\"normal\",\"viewCount\":2,\"workLocation\":\"北京\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:22:14', 14);
INSERT INTO `sys_oper_log` VALUES (271, '培训机构申请', 1, 'com.sux.web.controller.training.PublicTrainingInstitutionApplicationController.submitApplication()', 'POST', 1, 'admin', NULL, '/public/training/institution/application/submit', '127.0.0.1', '内网IP', '{\"applicationId\":3,\"applicationNote\":\"\",\"applicationStatus\":\"0\",\"applicationTime\":\"2025-07-23 22:23:24\",\"businessScope\":\"\",\"contactEmail\":\"\",\"contactPerson\":\"理你\",\"contactPhone\":\"***********\",\"createId\":1,\"createTime\":\"2025-07-23 22:23:24\",\"establishedDate\":\"2025-06-30\",\"facilityFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723222300A004.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723222300A004.png\\\"}]\",\"facilityInfo\":\"231\",\"institutionAddress\":\"421423432\",\"institutionCode\":\"\",\"institutionName\":\"jishi \",\"institutionType\":\"\",\"legalPerson\":\"321\",\"orderId\":8,\"params\":{},\"qualificationFiles\":\"[{\\\"name\\\":\\\"JAVA面经(1)_20250723222247A001.docx\\\",\\\"fileName\\\":\\\"JAVA面经(1)_20250723222247A001.docx\\\"}]\",\"teacherCertFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723222258A003.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723222258A003.png\\\"}]\",\"teacherInfo\":\"321\",\"trainingCapacity\":\"321\",\"trainingExperience\":\"321\",\"trainingPlan\":\"321\",\"trainingPlanFile\":\"[{\\\"name\\\":\\\"1_20250723222256A002.png\\\",\\\"fileName\\\":\\\"1_20250723222256A002.png\\\"}]\",\"userId\":1}', '{\"msg\":\"申请提交成功，请等待审核\",\"code\":200}', 0, NULL, '2025-07-23 22:23:24', 32);
INSERT INTO `sys_oper_log` VALUES (272, '培训机构申请审核', 2, 'com.sux.web.controller.training.TrainingInstitutionApplicationController.review()', 'PUT', 1, 'admin', NULL, '/training/institution/application/review/3', '127.0.0.1', '内网IP', '{\"reviewComment\":\"ces \",\"status\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:25:01', 11);
INSERT INTO `sys_oper_log` VALUES (273, '用户管理', 1, 'com.sux.web.controller.system.SysUserController.add()', 'POST', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"email\":\"\",\"nickName\":\"企业培训申请\",\"params\":{},\"postIds\":[],\"roleIds\":[105],\"status\":\"0\",\"userId\":104,\"userName\":\"企业培训申请\",\"userType\":\"00\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:37:45', 888);
INSERT INTO `sys_oper_log` VALUES (274, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-23 14:15:59\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[5069],\"params\":{},\"roleId\":105,\"roleKey\":\"企业培训申请\",\"roleName\":\"企业培训申请\",\"roleSort\":11,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:38:47', 40);
INSERT INTO `sys_oper_log` VALUES (275, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-23 14:15:59\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[5069],\"params\":{},\"roleId\":105,\"roleKey\":\"企业培训申请\",\"roleName\":\"机构培训申请\",\"roleSort\":11,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:38:55', 25);
INSERT INTO `sys_oper_log` VALUES (276, '培训机构申请', 2, 'com.sux.web.controller.training.TrainingInstitutionApplicationController.edit()', 'PUT', 1, 'admin', NULL, '/training/institution/application', '127.0.0.1', '内网IP', '{\"applicationId\":3,\"applicationNote\":\"\",\"applicationStatus\":\"1\",\"businessScope\":\"\",\"contactEmail\":\"\",\"contactPerson\":\"理你\",\"contactPhone\":\"***********\",\"establishedDate\":\"2025-06-30\",\"facilityFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723222300A004.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723222300A004.png\\\"}]\",\"facilityInfo\":\"231\",\"institutionAddress\":\"421423432\",\"institutionCode\":\"\",\"institutionName\":\"jishi \",\"institutionType\":\"\",\"legalPerson\":\"321\",\"orderId\":8,\"params\":{},\"qualificationFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723225003A001.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723225003A001.png\\\"}]\",\"teacherCertFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723222258A003.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723222258A003.png\\\"}]\",\"teacherInfo\":\"321\",\"trainingCapacity\":\"321\",\"trainingExperience\":\"321\",\"trainingPlan\":\"321\",\"trainingPlanFile\":\"[{\\\"name\\\":\\\"1_20250723222256A002.png\\\",\\\"fileName\\\":\\\"1_20250723222256A002.png\\\"}]\",\"updateTime\":\"2025-07-23 22:50:05\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:50:05', 36);
INSERT INTO `sys_oper_log` VALUES (277, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-23 00:01:37\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[3000,3002,3001,3010,3011,3012,3013,3014,4045],\"params\":{},\"roleId\":100,\"roleKey\":\"初审\",\"roleName\":\"企业就业服务初审\",\"roleSort\":1,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:55:40', 30);
INSERT INTO `sys_oper_log` VALUES (278, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-23 00:03:18\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[4047],\"params\":{},\"roleId\":102,\"roleKey\":\"企业就业\",\"roleName\":\"企业就业服务申请\",\"roleSort\":1,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:55:42', 13);
INSERT INTO `sys_oper_log` VALUES (279, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-23 00:01:59\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[3000,3002,3001,3010,3011,3012,3013,3014,4046],\"params\":{},\"roleId\":101,\"roleKey\":\"终审\",\"roleName\":\"企业就业服务终审\",\"roleSort\":1,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:55:45', 13);
INSERT INTO `sys_oper_log` VALUES (280, '角色管理', 1, 'com.sux.web.controller.system.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"dataScope\":\"1\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[5000,5001,5010,5011,5012,5013,5014,5015,5016,5017,5018,5002,5020,5021,5022,5023,5024,5025,5026,5027,5028,5029,5030,5031],\"params\":{},\"roleId\":106,\"roleKey\":\"零工市场\",\"roleName\":\"零工市场\",\"roleSort\":20,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:56:21', 12);
INSERT INTO `sys_oper_log` VALUES (281, '用户管理', 1, 'com.sux.web.controller.system.SysUserController.add()', 'POST', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"email\":\"\",\"nickName\":\"零工市场\",\"params\":{},\"postIds\":[],\"roleIds\":[106],\"status\":\"0\",\"userId\":105,\"userName\":\"零工市场\",\"userType\":\"00\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:56:42', 78);
INSERT INTO `sys_oper_log` VALUES (282, '个人信息', 2, 'com.sux.web.controller.system.SysProfileController.updatePwd()', 'PUT', 1, '零工市场', NULL, '/system/user/profile/updatePwd', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:57:12', 208);
INSERT INTO `sys_oper_log` VALUES (283, '零工信息', 2, 'com.sux.web.controller.job.WorkerProfileController.edit()', 'PUT', 1, '零工市场', NULL, '/job/worker', '127.0.0.1', '内网IP', '{\"completedJobs\":0,\"createId\":1,\"createTime\":\"2025-07-23 22:14:41\",\"currentLocation\":\"深圳\",\"delFlag\":\"0\",\"educationLevel\":\"初中\",\"gender\":\"female\",\"isVerified\":1,\"jobTypesPreferred\":\"全职\",\"lastActiveTime\":\"2025-07-23 22:14:41\",\"params\":{},\"phone\":\"16545454545\",\"ratingAverage\":0,\"ratingCount\":0,\"realName\":\"李某\",\"salaryExpectationMax\":49999,\"salaryExpectationMin\":1999,\"salaryTypePreference\":\"hourly\",\"status\":\"active\",\"successRate\":0,\"updateId\":1,\"updateTime\":\"2025-07-23 22:14:52\",\"userId\":1,\"userName\":\"admin\",\"workCategories\":\"搬运工\",\"workerId\":14}', '{\"msg\":\"修改零工信息失败，无权限或状态不允许修改\",\"code\":500}', 0, NULL, '2025-07-23 22:57:18', 6);
INSERT INTO `sys_oper_log` VALUES (284, '零工信息', 2, 'com.sux.web.controller.job.WorkerProfileController.edit()', 'PUT', 1, '零工市场', NULL, '/job/worker', '127.0.0.1', '内网IP', '{\"age\":25,\"availabilityStartDate\":\"2025-08-01\",\"completedJobs\":28,\"createId\":1,\"createTime\":\"2025-07-23 22:12:33\",\"currentLocation\":\"青岛市崂山区\",\"delFlag\":\"0\",\"educationLevel\":\"本科\",\"gender\":\"female\",\"isVerified\":1,\"jobTypesPreferred\":\"[\\\"全职\\\",\\\"兼职\\\"]\",\"lastActiveTime\":\"2025-07-23 22:12:33\",\"nickname\":\"慧慧\",\"params\":{},\"phone\":\"13900001003\",\"ratingAverage\":4.9,\"ratingCount\":32,\"realName\":\"王小慧\",\"remark\":\"星级酒店经验，高端服务专家\",\"salaryExpectationMax\":35,\"salaryExpectationMin\":25,\"salaryTypePreference\":\"hourly\",\"selfIntroduction\":\"有星级酒店服务经验，形象气质佳，会英语和日语，服务意识强，擅长高端客户服务\",\"skills\":\"[\\\"高端服务\\\",\\\"礼仪接待\\\",\\\"多语言\\\",\\\"红酒知识\\\"]\",\"status\":\"active\",\"successRate\":97.8,\"updateTime\":\"2025-07-23 22:12:33\",\"userId\":103,\"userName\":\"个人培训申请\",\"workCategories\":\"[\\\"服务员\\\"]\",\"workDaysPerWeek\":6,\"workExperienceYears\":3,\"workHoursPerDay\":8,\"workerId\":3}', '{\"msg\":\"修改零工信息失败，无权限或状态不允许修改\",\"code\":500}', 0, NULL, '2025-07-23 22:57:24', 2);
INSERT INTO `sys_oper_log` VALUES (285, '零工信息', 2, 'com.sux.web.controller.job.WorkerProfileController.edit()', 'PUT', 1, '零工市场', NULL, '/job/worker', '127.0.0.1', '内网IP', '{\"age\":30,\"availabilityStartDate\":\"2025-08-01\",\"completedJobs\":24,\"createId\":1,\"createTime\":\"2025-07-23 22:12:33\",\"currentLocation\":\"青岛市市南区\",\"delFlag\":\"0\",\"educationLevel\":\"大专\",\"gender\":\"male\",\"isVerified\":1,\"jobTypesPreferred\":\"[\\\"全职\\\"]\",\"lastActiveTime\":\"2025-07-23 22:12:33\",\"nickname\":\"销售达人\",\"params\":{},\"phone\":\"13900001008\",\"ratingAverage\":4.7,\"ratingCount\":28,\"realName\":\"赵小销\",\"remark\":\"房产销售精英，业绩突出\",\"salaryExpectationMax\":8000,\"salaryExpectationMin\":4000,\"salaryTypePreference\":\"monthly\",\"selfIntroduction\":\"有6年房产销售经验，熟悉青岛房产市场，客户资源丰富，业绩优秀，沟通能力强\",\"skills\":\"[\\\"房产销售\\\",\\\"客户开发\\\",\\\"合同谈判\\\",\\\"市场分析\\\"]\",\"status\":\"active\",\"successRate\":95.2,\"updateTime\":\"2025-07-23 22:12:33\",\"userId\":108,\"workCategories\":\"[\\\"销售\\\"]\",\"workDaysPerWeek\":6,\"workExperienceYears\":6,\"workHoursPerDay\":8,\"workerId\":8}', '{\"msg\":\"修改零工信息失败，无权限或状态不允许修改\",\"code\":500}', 0, NULL, '2025-07-23 22:57:42', 2);
INSERT INTO `sys_oper_log` VALUES (286, '零工信息', 2, 'com.sux.web.controller.job.WorkerProfileController.edit()', 'PUT', 1, '零工市场', NULL, '/job/worker', '127.0.0.1', '内网IP', '{\"age\":30,\"availabilityStartDate\":\"2025-08-01\",\"completedJobs\":24,\"createId\":1,\"createTime\":\"2025-07-23 22:12:33\",\"currentLocation\":\"青岛市市南区\",\"delFlag\":\"0\",\"educationLevel\":\"大专\",\"gender\":\"male\",\"isVerified\":1,\"jobTypesPreferred\":\"[\\\"全职\\\"]\",\"lastActiveTime\":\"2025-07-23 22:12:33\",\"nickname\":\"销售达人\",\"params\":{},\"phone\":\"13900001008\",\"ratingAverage\":4.7,\"ratingCount\":28,\"realName\":\"赵小销\",\"remark\":\"房产销售精英，业绩突出\",\"salaryExpectationMax\":8000,\"salaryExpectationMin\":4000,\"salaryTypePreference\":\"monthly\",\"selfIntroduction\":\"有6年房产销售经验，熟悉青岛房产市场，客户资源丰富，业绩优秀，沟通能力强\",\"skills\":\"[\\\"房产销售\\\",\\\"客户开发\\\",\\\"合同谈判\\\",\\\"市场分析\\\"]\",\"status\":\"active\",\"successRate\":95.2,\"updateTime\":\"2025-07-23 22:12:33\",\"userId\":108,\"workCategories\":\"[\\\"销售\\\"]\",\"workDaysPerWeek\":6,\"workExperienceYears\":6,\"workHoursPerDay\":8,\"workerId\":8}', '{\"msg\":\"修改零工信息失败，无权限或状态不允许修改\",\"code\":500}', 0, NULL, '2025-07-23 22:57:47', 5);
INSERT INTO `sys_oper_log` VALUES (287, '零工信息', 2, 'com.sux.web.controller.job.WorkerProfileController.edit()', 'PUT', 1, '零工市场', NULL, '/job/worker', '127.0.0.1', '内网IP', '{\"age\":30,\"availabilityStartDate\":\"2025-08-01\",\"completedJobs\":24,\"createId\":1,\"createTime\":\"2025-07-23 22:12:33\",\"currentLocation\":\"青岛市市南区\",\"delFlag\":\"0\",\"educationLevel\":\"大专\",\"gender\":\"male\",\"isVerified\":1,\"jobTypesPreferred\":\"[\\\"全职\\\"]\",\"lastActiveTime\":\"2025-07-23 22:12:33\",\"nickname\":\"销售达人\",\"params\":{},\"phone\":\"13900001008\",\"ratingAverage\":4.7,\"ratingCount\":28,\"realName\":\"赵小销\",\"remark\":\"房产销售精英，业绩突出\",\"salaryExpectationMax\":8000,\"salaryExpectationMin\":4000,\"salaryTypePreference\":\"monthly\",\"selfIntroduction\":\"有6年房产销售经验，熟悉青岛房产市场，客户资源丰富，业绩优秀，沟通能力强\",\"skills\":\"[\\\"房产销售\\\",\\\"客户开发\\\",\\\"合同谈判\\\",\\\"市场分析\\\"]\",\"status\":\"active\",\"successRate\":95.2,\"updateId\":105,\"updateTime\":\"2025-07-23 22:58:18\",\"userId\":108,\"workCategories\":\"[\\\"销售\\\"]\",\"workDaysPerWeek\":6,\"workExperienceYears\":6,\"workHoursPerDay\":8,\"workerId\":8}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:58:19', 160);
INSERT INTO `sys_oper_log` VALUES (288, '零工信息', 3, 'com.sux.web.controller.job.WorkerProfileController.remove()', 'DELETE', 1, '零工市场', NULL, '/job/worker/3', '127.0.0.1', '内网IP', '[3]', '{\"msg\":\"删除零工信息失败，无权限或状态不允许删除\",\"code\":500}', 0, NULL, '2025-07-23 22:58:21', 13);
INSERT INTO `sys_oper_log` VALUES (289, '零工信息', 2, 'com.sux.web.controller.job.WorkerProfileController.suspend()', 'PUT', 1, '零工市场', NULL, '/job/worker/suspend/3', '127.0.0.1', '内网IP', '3', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 22:58:25', 16);
INSERT INTO `sys_oper_log` VALUES (290, '零工信息', 3, 'com.sux.web.controller.job.WorkerProfileController.remove()', 'DELETE', 1, '零工市场', NULL, '/job/worker/3', '127.0.0.1', '内网IP', '[3]', '{\"msg\":\"删除零工信息失败，无权限或状态不允许删除\",\"code\":500}', 0, NULL, '2025-07-23 22:58:26', 9);
INSERT INTO `sys_oper_log` VALUES (291, '培训机构申请', 1, 'com.sux.web.controller.training.PublicTrainingInstitutionApplicationController.submitApplication()', 'POST', 1, 'admin', NULL, '/public/training/institution/application/submit', '127.0.0.1', '内网IP', '{\"applicationId\":4,\"applicationNote\":\"\",\"applicationStatus\":\"0\",\"applicationTime\":\"2025-07-23 23:01:28\",\"businessScope\":\"\",\"contactEmail\":\"\",\"contactPerson\":\"53\",\"contactPhone\":\"***********\",\"createId\":1,\"createTime\":\"2025-07-23 23:01:28\",\"facilityFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\"}]\",\"facilityInfo\":\"\",\"institutionAddress\":\"32143242\",\"institutionCode\":\"\",\"institutionName\":\"测试机构\",\"institutionType\":\"事业单位\",\"legalPerson\":\"15435413\",\"orderId\":7,\"params\":{},\"qualificationFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\"}]\",\"teacherCertFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\"}]\",\"teacherInfo\":\"343\",\"trainingCapacity\":\"43\",\"trainingExperience\":\"5343\",\"trainingPlan\":\"4\",\"trainingPlanFile\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\"}]\",\"userId\":1}', '{\"msg\":\"申请提交成功，请等待审核\",\"code\":200}', 0, NULL, '2025-07-23 23:01:29', 479);
INSERT INTO `sys_oper_log` VALUES (292, '零工信息', 2, 'com.sux.web.controller.job.WorkerProfileController.edit()', 'PUT', 1, '零工市场', NULL, '/job/worker', '127.0.0.1', '内网IP', '{\"age\":22,\"availabilityStartDate\":\"2025-07-25\",\"completedJobs\":18,\"createId\":1,\"createTime\":\"2025-07-23 22:12:33\",\"currentLocation\":\"青岛市市南区\",\"delFlag\":\"0\",\"educationLevel\":\"高中\",\"gender\":\"female\",\"isVerified\":1,\"jobTypesPreferred\":\"[\\\"兼职\\\",\\\"临时工\\\"]\",\"lastActiveTime\":\"2025-07-23 22:12:33\",\"nickname\":\"美美服务员\",\"params\":{},\"phone\":\"13900001001\",\"ratingAverage\":4.8,\"ratingCount\":25,\"realName\":\"张小美\",\"remark\":\"优秀服务员，客户评价很高\",\"salaryExpectationMax\":30,\"salaryExpectationMin\":20,\"salaryTypePreference\":\"hourly\",\"selfIntroduction\":\"有2年餐厅服务经验，服务态度好，沟通能力强，熟悉收银系统操作，形象气质佳\",\"skills\":\"[\\\"餐厅服务\\\",\\\"客户接待\\\",\\\"收银操作\\\",\\\"礼仪服务\\\"]\",\"status\":\"active\",\"successRate\":95.5,\"updateId\":105,\"updateTime\":\"2025-07-23 23:03:51\",\"userId\":101,\"userName\":\"admin_2\",\"workCategories\":\"[\\\"服务员\\\"]\",\"workDaysPerWeek\":6,\"workExperienceYears\":2,\"workHoursPerDay\":8,\"workerId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 23:03:51', 58);
INSERT INTO `sys_oper_log` VALUES (293, '用户管理', 1, 'com.sux.web.controller.system.SysUserController.add()', 'POST', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"email\":\"\",\"nickName\":\"企业就业培训管理\",\"params\":{},\"postIds\":[],\"roleIds\":[103],\"status\":\"0\",\"userId\":106,\"userName\":\"企业就业培训管理\",\"userType\":\"00\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 23:05:26', 264);
INSERT INTO `sys_oper_log` VALUES (294, '个人信息', 2, 'com.sux.web.controller.system.SysProfileController.updatePwd()', 'PUT', 1, '企业就业培训管理', NULL, '/system/user/profile/updatePwd', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 23:05:52', 593);
INSERT INTO `sys_oper_log` VALUES (295, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-23 14:15:17\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[4048,3003,3030,3031,3032,3033,3034,3035,3036,5060,5061,5062,5063,5064,5065,5066,5067,5070,5071,5072,5073,5074,5075,5076,5077],\"params\":{},\"roleId\":103,\"roleKey\":\"企业就业培训管理\",\"roleName\":\"企业就业培训管理\",\"roleSort\":10,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 23:06:12', 50);
INSERT INTO `sys_oper_log` VALUES (296, '培训报名', 2, 'com.sux.web.controller.training.TrainingApplicationController.edit()', 'PUT', 1, '企业就业培训管理', NULL, '/training/application', '127.0.0.1', '内网IP', '{\"applicantAddress\":\"\",\"applicantEducation\":\"小学\",\"applicantEmail\":\"\",\"applicantExperience\":\"\",\"applicantGender\":\"\",\"applicantIdCard\":\"\",\"applicantName\":\"12321\",\"applicantPhone\":\"17574857483\",\"applicationId\":6,\"applicationNote\":\"\",\"applicationStatus\":\"3\",\"applicationTime\":\"2025-07-23 19:24:52\",\"createId\":1,\"createTime\":\"2025-07-23 19:24:52\",\"delFlag\":\"0\",\"endDate\":\"2025-08-22 18:00:00\",\"orderId\":7,\"orderTitle\":\"财务分析与预算管理\",\"params\":{},\"startDate\":\"2025-08-18 09:00:00\",\"trainingType\":\"管理培训\",\"updateTime\":\"2025-07-23 19:24:57\",\"userId\":1}', '{\"msg\":\"修改培训报名失败，该手机号或用户已报名此培训\",\"code\":500}', 0, NULL, '2025-07-23 23:06:19', 20);
INSERT INTO `sys_oper_log` VALUES (297, '培训报名', 2, 'com.sux.web.controller.training.TrainingApplicationController.edit()', 'PUT', 1, '企业就业培训管理', NULL, '/training/application', '127.0.0.1', '内网IP', '{\"applicantAddress\":\"\",\"applicantEducation\":\"小学\",\"applicantEmail\":\"\",\"applicantExperience\":\"\",\"applicantGender\":\"\",\"applicantIdCard\":\"\",\"applicantName\":\"12321\",\"applicantPhone\":\"17574857483\",\"applicationId\":7,\"applicationNote\":\"\",\"applicationStatus\":\"0\",\"applicationTime\":\"2025-07-23 19:24:59\",\"createId\":1,\"createTime\":\"2025-07-23 19:24:59\",\"delFlag\":\"0\",\"endDate\":\"2025-08-22 18:00:00\",\"orderId\":7,\"orderTitle\":\"财务分析与预算管理\",\"params\":{},\"startDate\":\"2025-08-18 09:00:00\",\"trainingType\":\"管理培训\",\"updateTime\":\"2025-07-23 23:06:33\",\"userId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 23:06:33', 25);
INSERT INTO `sys_oper_log` VALUES (298, '培训报名', 2, 'com.sux.web.controller.training.TrainingApplicationController.edit()', 'PUT', 1, '企业就业培训管理', NULL, '/training/application', '127.0.0.1', '内网IP', '{\"applicantAddress\":\"\",\"applicantEducation\":\"\",\"applicantEmail\":\"\",\"applicantExperience\":\"\",\"applicantGender\":\"\",\"applicantIdCard\":\"\",\"applicantName\":\"你好\",\"applicantPhone\":\"15454545454\",\"applicationId\":5,\"applicationNote\":\"\",\"applicationStatus\":\"1\",\"applicationTime\":\"2025-07-23 18:36:37\",\"createId\":1,\"createTime\":\"2025-07-23 18:36:37\",\"delFlag\":\"0\",\"endDate\":\"2025-09-05 18:00:00\",\"orderId\":8,\"orderTitle\":\"人工智能与机器学习入门\",\"params\":{},\"reviewComment\":\"测试\",\"reviewTime\":\"2025-07-23 18:43:48\",\"reviewer\":\"admin\",\"startDate\":\"2025-08-20 09:00:00\",\"trainingType\":\"技术培训\",\"updateTime\":\"2025-07-23 23:07:01\",\"userId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 23:07:01', 20);
INSERT INTO `sys_oper_log` VALUES (299, '培训报名', 2, 'com.sux.web.controller.training.TrainingApplicationController.edit()', 'PUT', 1, '企业就业培训管理', NULL, '/training/application', '127.0.0.1', '内网IP', '{\"applicantAddress\":\"北京市西城区\",\"applicantAge\":30,\"applicantEducation\":\"大专\",\"applicantEmail\":\"<EMAIL>\",\"applicantExperience\":\"有2年数据分析经验，熟悉Excel和SQL\",\"applicantGender\":\"男\",\"applicantIdCard\":\"110101199003031234\",\"applicantName\":\"王五\",\"applicantPhone\":\"13800138003\",\"applicationId\":3,\"applicationNote\":\"想转行做数据分析师\",\"applicationStatus\":\"1\",\"applicationTime\":\"2025-07-21 11:00:00\",\"createTime\":\"2025-07-21 11:00:00\",\"delFlag\":\"0\",\"endDate\":\"2025-08-12 18:00:00\",\"orderId\":2,\"orderTitle\":\"Python数据分析师培训\",\"params\":{},\"reviewComment\":\"基础扎实，适合参加培训\",\"reviewTime\":\"2025-07-21 15:30:00\",\"reviewer\":\"张老师\",\"startDate\":\"2025-08-05 09:00:00\",\"trainingType\":\"技术培训\",\"updateTime\":\"2025-07-23 23:07:03\",\"userId\":3}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 23:07:03', 22);
INSERT INTO `sys_oper_log` VALUES (300, '培训报名', 2, 'com.sux.web.controller.training.TrainingApplicationController.edit()', 'PUT', 1, '企业就业培训管理', NULL, '/training/application', '127.0.0.1', '内网IP', '{\"applicantAddress\":\"\",\"applicantEducation\":\"\",\"applicantEmail\":\"\",\"applicantExperience\":\"\",\"applicantGender\":\"\",\"applicantIdCard\":\"\",\"applicantName\":\"你好\",\"applicantPhone\":\"15454545454\",\"applicationId\":4,\"applicationNote\":\"\",\"applicationStatus\":\"3\",\"applicationTime\":\"2025-07-23 18:36:29\",\"createId\":1,\"createTime\":\"2025-07-23 18:36:29\",\"delFlag\":\"0\",\"endDate\":\"2025-09-05 18:00:00\",\"orderId\":8,\"orderTitle\":\"人工智能与机器学习入门\",\"params\":{},\"startDate\":\"2025-08-20 09:00:00\",\"trainingType\":\"技术培训\",\"updateTime\":\"2025-07-23 18:36:33\",\"userId\":1}', '{\"msg\":\"修改培训报名失败，该手机号或用户已报名此培训\",\"code\":500}', 0, NULL, '2025-07-23 23:07:05', 11);
INSERT INTO `sys_oper_log` VALUES (301, '培训报名', 2, 'com.sux.web.controller.training.TrainingApplicationController.edit()', 'PUT', 1, '企业就业培训管理', NULL, '/training/application', '127.0.0.1', '内网IP', '{\"applicantAddress\":\"\",\"applicantEducation\":\"\",\"applicantEmail\":\"\",\"applicantExperience\":\"\",\"applicantGender\":\"\",\"applicantIdCard\":\"\",\"applicantName\":\"你好\",\"applicantPhone\":\"15454545412\",\"applicationId\":4,\"applicationNote\":\"\",\"applicationStatus\":\"3\",\"applicationTime\":\"2025-07-23 18:36:29\",\"createId\":1,\"createTime\":\"2025-07-23 18:36:29\",\"delFlag\":\"0\",\"endDate\":\"2025-09-05 18:00:00\",\"orderId\":8,\"orderTitle\":\"人工智能与机器学习入门\",\"params\":{},\"startDate\":\"2025-08-20 09:00:00\",\"trainingType\":\"技术培训\",\"updateTime\":\"2025-07-23 18:36:33\",\"userId\":1}', '{\"msg\":\"修改培训报名失败，该手机号或用户已报名此培训\",\"code\":500}', 0, NULL, '2025-07-23 23:07:09', 11);
INSERT INTO `sys_oper_log` VALUES (302, '培训报名', 2, 'com.sux.web.controller.training.TrainingApplicationController.edit()', 'PUT', 1, '企业就业培训管理', NULL, '/training/application', '127.0.0.1', '内网IP', '{\"applicantAddress\":\"北京市西城区\",\"applicantAge\":30,\"applicantEducation\":\"大专\",\"applicantEmail\":\"<EMAIL>\",\"applicantExperience\":\"有2年数据分析经验，熟悉Excel和SQL\",\"applicantGender\":\"男\",\"applicantIdCard\":\"110101199003031234\",\"applicantName\":\"王五\",\"applicantPhone\":\"13800138003\",\"applicationId\":3,\"applicationNote\":\"想转行做数据分析师\",\"applicationStatus\":\"1\",\"applicationTime\":\"2025-07-21 11:00:00\",\"createTime\":\"2025-07-21 11:00:00\",\"delFlag\":\"0\",\"endDate\":\"2025-08-12 18:00:00\",\"orderId\":2,\"orderTitle\":\"Python数据分析师培训\",\"params\":{},\"reviewComment\":\"基础扎实，适合参加培训\",\"reviewTime\":\"2025-07-21 15:30:00\",\"reviewer\":\"张老师\",\"startDate\":\"2025-08-05 09:00:00\",\"trainingType\":\"技术培训\",\"updateTime\":\"2025-07-23 23:07:20\",\"userId\":3}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 23:07:20', 23);
INSERT INTO `sys_oper_log` VALUES (303, '取消培训机构申请', 2, 'com.sux.web.controller.training.PublicTrainingInstitutionApplicationController.cancelMyApplication()', 'PUT', 1, 'admin', NULL, '/public/training/institution/application/cancel/4', '127.0.0.1', '内网IP', '4', '{\"msg\":\"取消申请成功\",\"code\":200}', 0, NULL, '2025-07-23 23:07:51', 19);
INSERT INTO `sys_oper_log` VALUES (304, '培训机构申请', 1, 'com.sux.web.controller.training.PublicTrainingInstitutionApplicationController.submitApplication()', 'POST', 1, 'admin', NULL, '/public/training/institution/application/submit', '127.0.0.1', '内网IP', '{\"applicationNote\":\"\",\"businessScope\":\"\",\"contactEmail\":\"\",\"contactPerson\":\"53\",\"contactPhone\":\"***********\",\"facilityFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\"}]\",\"facilityInfo\":\"\",\"institutionAddress\":\"32143242\",\"institutionCode\":\"\",\"institutionName\":\"测试机构\",\"institutionType\":\"事业单位\",\"legalPerson\":\"15435413\",\"orderId\":7,\"otherFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230801A005.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230801A005.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230801A005.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230801A005.png\\\"}]\",\"params\":{},\"qualificationFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\"}]\",\"teacherCertFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\"}]\",\"teacherInfo\":\"343\",\"trainingCapacity\":\"43\",\"trainingExperience\":\"5343\",\"trainingPlan\":\"4\",\"trainingPlanFile\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/up', '{\"msg\":\"您的机构已申请此培训，请勿重复申请\",\"code\":500}', 0, NULL, '2025-07-23 23:08:03', 10);
INSERT INTO `sys_oper_log` VALUES (305, '培训机构申请', 1, 'com.sux.web.controller.training.PublicTrainingInstitutionApplicationController.submitApplication()', 'POST', 1, 'admin', NULL, '/public/training/institution/application/submit', '127.0.0.1', '内网IP', '{\"applicationNote\":\"\",\"businessScope\":\"\",\"contactEmail\":\"\",\"contactPerson\":\"53\",\"contactPhone\":\"***********\",\"facilityFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\"}]\",\"facilityInfo\":\"\",\"institutionAddress\":\"32143242\",\"institutionCode\":\"\",\"institutionName\":\"测试机构\",\"institutionType\":\"事业单位\",\"legalPerson\":\"15435413\",\"orderId\":7,\"otherFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230801A005.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230801A005.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230801A005.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230801A005.png\\\"}]\",\"params\":{},\"qualificationFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\"}]\",\"teacherCertFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\"}]\",\"teacherInfo\":\"343\",\"trainingCapacity\":\"43\",\"trainingExperience\":\"5343\",\"trainingPlan\":\"4\",\"trainingPlanFile\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/up', '{\"msg\":\"您的机构已申请此培训，请勿重复申请\",\"code\":500}', 0, NULL, '2025-07-23 23:08:09', 5);
INSERT INTO `sys_oper_log` VALUES (306, '培训机构申请', 1, 'com.sux.web.controller.training.PublicTrainingInstitutionApplicationController.submitApplication()', 'POST', 1, 'admin', NULL, '/public/training/institution/application/submit', '127.0.0.1', '内网IP', '{\"applicationNote\":\"\",\"businessScope\":\"\",\"contactEmail\":\"\",\"contactPerson\":\"53\",\"contactPhone\":\"***********\",\"facilityFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\"}]\",\"facilityInfo\":\"\",\"institutionAddress\":\"32143242\",\"institutionCode\":\"\",\"institutionName\":\"测试机构\",\"institutionType\":\"事业单位\",\"legalPerson\":\"15435413\",\"orderId\":7,\"otherFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230801A005.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230801A005.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230801A005.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230801A005.png\\\"}]\",\"params\":{},\"qualificationFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\"}]\",\"teacherCertFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\"}]\",\"teacherInfo\":\"343\",\"trainingCapacity\":\"43\",\"trainingExperience\":\"5343\",\"trainingPlan\":\"4\",\"trainingPlanFile\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/up', '{\"msg\":\"您的机构已申请此培训，请勿重复申请\",\"code\":500}', 0, NULL, '2025-07-23 23:08:14', 8);
INSERT INTO `sys_oper_log` VALUES (307, '培训机构申请', 1, 'com.sux.web.controller.training.PublicTrainingInstitutionApplicationController.submitApplication()', 'POST', 1, 'admin', NULL, '/public/training/institution/application/submit', '127.0.0.1', '内网IP', '{\"applicationNote\":\"\",\"businessScope\":\"\",\"contactEmail\":\"\",\"contactPerson\":\"53\",\"contactPhone\":\"***********\",\"facilityFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\"}]\",\"facilityInfo\":\"\",\"institutionAddress\":\"32143242\",\"institutionCode\":\"\",\"institutionName\":\"测试机构\",\"institutionType\":\"事业单位\",\"legalPerson\":\"15435413\",\"orderId\":7,\"params\":{},\"qualificationFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\"}]\",\"teacherCertFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\"}]\",\"teacherInfo\":\"343\",\"trainingCapacity\":\"43\",\"trainingExperience\":\"5343\",\"trainingPlan\":\"4\",\"trainingPlanFile\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\"}]\"}', '{\"msg\":\"您的机构已申请此培训，请勿重复申请\",\"code\":500}', 0, NULL, '2025-07-23 23:08:17', 10);
INSERT INTO `sys_oper_log` VALUES (308, '培训机构申请', 1, 'com.sux.web.controller.training.PublicTrainingInstitutionApplicationController.submitApplication()', 'POST', 1, 'admin', NULL, '/public/training/institution/application/submit', '127.0.0.1', '内网IP', '{\"applicationNote\":\"\",\"businessScope\":\"\",\"contactEmail\":\"\",\"contactPerson\":\"53\",\"contactPhone\":\"***********\",\"facilityFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\"}]\",\"facilityInfo\":\"\",\"institutionAddress\":\"32143242\",\"institutionCode\":\"\",\"institutionName\":\"测试机构\",\"institutionType\":\"事业单位\",\"legalPerson\":\"15435413\",\"orderId\":7,\"params\":{},\"qualificationFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\"}]\",\"teacherCertFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\"}]\",\"teacherInfo\":\"343\",\"trainingCapacity\":\"43\",\"trainingExperience\":\"5343\",\"trainingPlan\":\"4\",\"trainingPlanFile\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\"}]\"}', '{\"msg\":\"您的机构已申请此培训，请勿重复申请\",\"code\":500}', 0, NULL, '2025-07-23 23:09:21', 8);
INSERT INTO `sys_oper_log` VALUES (309, '培训机构申请', 1, 'com.sux.web.controller.training.PublicTrainingInstitutionApplicationController.submitApplication()', 'POST', 1, 'admin', NULL, '/public/training/institution/application/submit', '127.0.0.1', '内网IP', '{\"applicationNote\":\"\",\"businessScope\":\"\",\"contactEmail\":\"\",\"contactPerson\":\"53\",\"contactPhone\":\"***********\",\"facilityFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\"}]\",\"facilityInfo\":\"\",\"institutionAddress\":\"32143242\",\"institutionCode\":\"\",\"institutionName\":\"测试机构\",\"institutionType\":\"事业单位\",\"legalPerson\":\"15435413\",\"orderId\":7,\"params\":{},\"qualificationFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\"}]\",\"teacherCertFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\"}]\",\"teacherInfo\":\"343\",\"trainingCapacity\":\"43\",\"trainingExperience\":\"5343\",\"trainingPlan\":\"4\",\"trainingPlanFile\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\"}]\"}', '{\"msg\":\"您的机构已申请此培训，请勿重复申请\",\"code\":500}', 0, NULL, '2025-07-23 23:09:26', 10);
INSERT INTO `sys_oper_log` VALUES (310, '培训机构申请', 1, 'com.sux.web.controller.training.PublicTrainingInstitutionApplicationController.submitApplication()', 'POST', 1, 'admin', NULL, '/public/training/institution/application/submit', '127.0.0.1', '内网IP', '{\"applicationNote\":\"\",\"businessScope\":\"\",\"contactEmail\":\"\",\"contactPerson\":\"53\",\"contactPhone\":\"***********\",\"facilityFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\"}]\",\"facilityInfo\":\"\",\"institutionAddress\":\"32143242\",\"institutionCode\":\"\",\"institutionName\":\"测试机构\",\"institutionType\":\"事业单位\",\"legalPerson\":\"15435413\",\"orderId\":7,\"params\":{},\"qualificationFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\"}]\",\"teacherCertFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\"}]\",\"teacherInfo\":\"343\",\"trainingCapacity\":\"43\",\"trainingExperience\":\"5343\",\"trainingPlan\":\"4\",\"trainingPlanFile\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\"}]\"}', '{\"msg\":\"您的机构已申请此培训，请勿重复申请\",\"code\":500}', 0, NULL, '2025-07-23 23:13:01', 10);
INSERT INTO `sys_oper_log` VALUES (311, '培训机构申请', 2, 'com.sux.web.controller.training.PublicTrainingInstitutionApplicationController.updateApplication()', 'PUT', 1, 'admin', NULL, '/public/training/institution/application/update', '127.0.0.1', '内网IP', '{\"applicationId\":4,\"applicationNote\":\"\",\"applicationStatus\":\"0\",\"applicationTime\":\"2025-07-23 23:16:24\",\"businessScope\":\"\",\"contactEmail\":\"\",\"contactPerson\":\"53\",\"contactPhone\":\"***********\",\"facilityFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\"}]\",\"facilityInfo\":\"\",\"institutionAddress\":\"32143242\",\"institutionCode\":\"\",\"institutionName\":\"测试机构\",\"institutionType\":\"事业单位\",\"legalPerson\":\"15435413\",\"orderId\":7,\"params\":{},\"qualificationFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\"}]\",\"teacherCertFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\"}]\",\"teacherInfo\":\"343\",\"trainingCapacity\":\"43\",\"trainingExperience\":\"5343\",\"trainingPlan\":\"4\",\"trainingPlanFile\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\"}]\",\"updateTime\":\"2025-07-23 23:16:24\"}', '{\"msg\":\"重新申请提交成功，请等待审核\",\"code\":200}', 0, NULL, '2025-07-23 23:16:24', 152);
INSERT INTO `sys_oper_log` VALUES (312, '取消培训机构申请', 2, 'com.sux.web.controller.training.PublicTrainingInstitutionApplicationController.cancelMyApplication()', 'PUT', 1, 'admin', NULL, '/public/training/institution/application/cancel/4', '127.0.0.1', '内网IP', '4', '{\"msg\":\"取消申请成功\",\"code\":200}', 0, NULL, '2025-07-23 23:16:27', 45);
INSERT INTO `sys_oper_log` VALUES (313, '培训机构申请', 2, 'com.sux.web.controller.training.PublicTrainingInstitutionApplicationController.updateApplication()', 'PUT', 1, 'admin', NULL, '/public/training/institution/application/update', '127.0.0.1', '内网IP', '{\"applicationId\":4,\"applicationNote\":\"\",\"applicationStatus\":\"0\",\"applicationTime\":\"2025-07-23 23:16:30\",\"businessScope\":\"\",\"contactEmail\":\"\",\"contactPerson\":\"53\",\"contactPhone\":\"***********\",\"facilityFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\\\"}]\",\"facilityInfo\":\"\",\"institutionAddress\":\"32143242\",\"institutionCode\":\"\",\"institutionName\":\"测试机构\",\"institutionType\":\"事业单位\",\"legalPerson\":\"15435413\",\"orderId\":7,\"params\":{},\"qualificationFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\\\"}]\",\"teacherCertFiles\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\\\"}]\",\"teacherInfo\":\"343\",\"trainingCapacity\":\"43\",\"trainingExperience\":\"5343\",\"trainingPlan\":\"4\",\"trainingPlanFile\":\"[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"fileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"url\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\\\"}]\",\"updateTime\":\"2025-07-23 23:16:30\"}', '{\"msg\":\"重新申请提交成功，请等待审核\",\"code\":200}', 0, NULL, '2025-07-23 23:16:30', 48);
INSERT INTO `sys_oper_log` VALUES (314, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/application/institution-apply\",\"createTime\":\"2025-07-23 21:57:14\",\"icon\":\"component\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuId\":5069,\"menuName\":\"机构培训申请\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"order/application/institution-apply\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"修改菜单\'机构培训申请\'失败，地址必须以http(s)://开头\",\"code\":500}', 0, NULL, '2025-07-23 23:20:30', 6);
INSERT INTO `sys_oper_log` VALUES (315, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/application/institution-apply\",\"createTime\":\"2025-07-23 21:57:14\",\"icon\":\"component\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5069,\"menuName\":\"机构培训申请\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"order/application/institution-apply\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 23:24:28', 9);
INSERT INTO `sys_oper_log` VALUES (316, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/application/institution\",\"createTime\":\"2025-07-23 21:57:14\",\"icon\":\"component\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5069,\"menuName\":\"机构培训申请\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"order/application/institution\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 23:26:01', 8);
INSERT INTO `sys_oper_log` VALUES (317, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/application/institution-apply\",\"createTime\":\"2025-07-23 21:57:14\",\"icon\":\"component\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5069,\"menuName\":\"机构培训申请\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"order/application/institution\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 23:26:13', 8);
INSERT INTO `sys_oper_log` VALUES (318, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/application/institution-apply\",\"createTime\":\"2025-07-23 21:57:14\",\"icon\":\"component\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5069,\"menuName\":\"机构培训申请\",\"menuType\":\"M\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"order/application/institution\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 23:27:24', 10);
INSERT INTO `sys_oper_log` VALUES (319, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/application/signup\",\"createTime\":\"2025-07-23 14:14:09\",\"icon\":\"edit\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5059,\"menuName\":\"个人培训申请\",\"menuType\":\"M\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"order/application/signup\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 23:27:35', 7);
INSERT INTO `sys_oper_log` VALUES (320, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2025-07-22 00:00:00\",\"icon\":\"peoples\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5000,\"menuName\":\"零工市场\",\"menuType\":\"M\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"job\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 23:29:31', 9);
INSERT INTO `sys_oper_log` VALUES (321, '用工信息', 2, 'com.sux.web.controller.place.EmploymentInfoController.edit()', 'PUT', 1, 'admin', NULL, '/place/employment', '127.0.0.1', '内网IP', '{\"ageMax\":50,\"ageMin\":20,\"applicationCount\":0,\"applicationDeadline\":\"2025-07-25 12:00:00\",\"companyAddress\":\"青岛市市北区重庆南路150号\",\"companyDescription\":\"大型物流企业，业务量稳定\",\"companyName\":\"青岛物流公司\",\"contactEmail\":\"<EMAIL>\",\"contactPerson\":\"王总\",\"contactPhone\":\"13700137001\",\"createId\":1,\"createTime\":\"2025-07-23 23:46:56\",\"delFlag\":\"0\",\"educationRequired\":\"不限\",\"employmentId\":3,\"employmentType\":\"日结\",\"endDate\":\"2025-08-10\",\"experienceRequired\":\"身体健康，能吃苦耐劳\",\"genderRequired\":\"male\",\"isFeatured\":1,\"isVerified\":1,\"params\":{},\"positionsFilled\":0,\"positionsNeeded\":10,\"publisherUserId\":1003,\"regionCode\":\"370203\",\"regionName\":\"市北区\",\"salaryMax\":250,\"salaryMin\":200,\"salaryType\":\"daily\",\"skillsRequired\":\"[\\\"体力劳动\\\", \\\"货物搬运\\\", \\\"团队协作\\\"]\",\"startDate\":\"2025-07-24\",\"status\":\"published\",\"title\":\"搬运工临时招聘\",\"updateId\":1,\"updateTime\":\"2025-07-24 00:00:34\",\"urgencyLevel\":\"urgent\",\"viewCount\":0,\"welfareBenefits\":\"按日结算，多劳多得，提供工作餐\",\"workCategory\":\"搬运工\",\"workDaysPerWeek\":7,\"workDescription\":\"负责货物搬运工作，要求身体健康，能够承受重体力劳动\",\"workHoursPerDay\":10,\"workLocation\":\"青岛市市北区重庆南路\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 00:00:34', 127);
INSERT INTO `sys_oper_log` VALUES (322, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"place/employment/index\",\"createTime\":\"2025-07-23 23:54:30\",\"icon\":\"peoples\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":4001,\"menuName\":\"用工信息\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":5000,\"path\":\"employment\",\"perms\":\"place:employment:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 00:00:58', 42);
INSERT INTO `sys_oper_log` VALUES (323, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"place/info/index\",\"createTime\":\"2025-07-23 23:54:30\",\"icon\":\"component\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":4002,\"menuName\":\"场地信息\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":5000,\"path\":\"info\",\"perms\":\"place:info:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 00:01:08', 20);
INSERT INTO `sys_oper_log` VALUES (324, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/4000', '127.0.0.1', '内网IP', '4000', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 00:01:10', 25);
INSERT INTO `sys_oper_log` VALUES (325, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-23 22:56:21\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[5000,4001,4101,4102,4103,4104,4105,4106,4107,4108,5001,5010,5011,5012,5013,5014,5015,5016,5017,5018,4002,4201,4202,4203,4204,4205,4206,4207,5002,5020,5021,5022,5023,5024,5025,5026,5027,5028,5029,5030,5031],\"params\":{},\"roleId\":106,\"roleKey\":\"零工市场\",\"roleName\":\"零工市场\",\"roleSort\":20,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 00:01:17', 97);
INSERT INTO `sys_oper_log` VALUES (326, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"place/employment/index\",\"createTime\":\"2025-07-23 23:54:30\",\"icon\":\"peoples\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":4001,\"menuName\":\"用工信息\",\"menuType\":\"C\",\"orderNum\":6,\"params\":{},\"parentId\":5000,\"path\":\"employment\",\"perms\":\"place:employment:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 00:01:25', 21);
INSERT INTO `sys_oper_log` VALUES (327, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"zhaop/index\",\"createTime\":\"2025-07-22 00:00:00\",\"icon\":\"post\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5001,\"menuName\":\"招聘信息\",\"menuType\":\"C\",\"orderNum\":7,\"params\":{},\"parentId\":5000,\"path\":\"posting\",\"perms\":\"job:posting:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 00:01:30', 24);
INSERT INTO `sys_oper_log` VALUES (328, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"place/info/index\",\"createTime\":\"2025-07-23 23:54:30\",\"icon\":\"component\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":4002,\"menuName\":\"场地信息\",\"menuType\":\"C\",\"orderNum\":7,\"params\":{},\"parentId\":5000,\"path\":\"info\",\"perms\":\"place:info:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 00:01:38', 21);
INSERT INTO `sys_oper_log` VALUES (329, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"zhaop/index\",\"createTime\":\"2025-07-22 00:00:00\",\"icon\":\"post\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":5001,\"menuName\":\"招聘信息\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":5000,\"path\":\"posting\",\"perms\":\"job:posting:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 00:01:42', 20);
INSERT INTO `sys_oper_log` VALUES (330, '用户管理', 2, 'com.sux.web.controller.system.SysUserController.resetPwd()', 'PUT', 1, 'admin', NULL, '/system/user/resetPwd', '127.0.0.1', '内网IP', '{\"admin\":false,\"params\":{},\"updateId\":1,\"userId\":2}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 00:02:57', 186);
INSERT INTO `sys_oper_log` VALUES (331, '用户管理', 2, 'com.sux.web.controller.system.SysUserController.resetPwd()', 'PUT', 1, 'admin', NULL, '/system/user/resetPwd', '127.0.0.1', '内网IP', '{\"admin\":false,\"params\":{},\"updateId\":1,\"userId\":100}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 00:03:03', 191);
INSERT INTO `sys_oper_log` VALUES (332, '用户管理', 2, 'com.sux.web.controller.system.SysUserController.resetPwd()', 'PUT', 1, 'admin', NULL, '/system/user/resetPwd', '127.0.0.1', '内网IP', '{\"admin\":false,\"params\":{},\"updateId\":1,\"userId\":101}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 00:03:05', 184);
INSERT INTO `sys_oper_log` VALUES (333, '用户管理', 2, 'com.sux.web.controller.system.SysUserController.resetPwd()', 'PUT', 1, 'admin', NULL, '/system/user/resetPwd', '127.0.0.1', '内网IP', '{\"admin\":false,\"params\":{},\"updateId\":1,\"userId\":102}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 00:03:09', 188);
INSERT INTO `sys_oper_log` VALUES (334, '用户管理', 2, 'com.sux.web.controller.system.SysUserController.resetPwd()', 'PUT', 1, 'admin', NULL, '/system/user/resetPwd', '127.0.0.1', '内网IP', '{\"admin\":false,\"params\":{},\"updateId\":1,\"userId\":103}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 00:03:12', 190);
INSERT INTO `sys_oper_log` VALUES (335, '用户管理', 2, 'com.sux.web.controller.system.SysUserController.resetPwd()', 'PUT', 1, 'admin', NULL, '/system/user/resetPwd', '127.0.0.1', '内网IP', '{\"admin\":false,\"params\":{},\"updateId\":1,\"userId\":104}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 00:03:16', 203);
INSERT INTO `sys_oper_log` VALUES (336, '用户管理', 2, 'com.sux.web.controller.system.SysUserController.resetPwd()', 'PUT', 1, 'admin', NULL, '/system/user/resetPwd', '127.0.0.1', '内网IP', '{\"admin\":false,\"params\":{},\"updateId\":1,\"userId\":105}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 00:03:18', 193);
INSERT INTO `sys_oper_log` VALUES (337, '用户管理', 2, 'com.sux.web.controller.system.SysUserController.resetPwd()', 'PUT', 1, 'admin', NULL, '/system/user/resetPwd', '127.0.0.1', '内网IP', '{\"admin\":false,\"params\":{},\"updateId\":1,\"userId\":106}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 00:03:20', 186);

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int(0) NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '岗位信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_post
-- ----------------------------
INSERT INTO `sys_post` VALUES (1, 'ceo', '董事长', 1, '0', 1, '2025-06-07 19:23:02', 1, '2025-06-28 17:15:34', '');

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int(0) NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 106 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '超级管理员');
INSERT INTO `sys_role` VALUES (2, '普通角色', 'common', 2, '2', 1, 1, '0', '2', 1, '2025-06-07 19:23:02', 1, '2025-06-28 16:39:37', '普通角色');
INSERT INTO `sys_role` VALUES (100, '企业就业服务初审', '初审', 1, '1', 1, 1, '0', '0', 1, '2025-07-23 00:01:37', 1, '2025-07-23 22:55:39', NULL);
INSERT INTO `sys_role` VALUES (101, '企业就业服务终审', '终审', 1, '1', 1, 1, '0', '0', 1, '2025-07-23 00:01:59', 1, '2025-07-23 22:55:45', NULL);
INSERT INTO `sys_role` VALUES (102, '企业就业服务申请', '企业就业', 1, '1', 1, 1, '0', '0', 1, '2025-07-23 00:03:18', 1, '2025-07-23 22:55:42', NULL);
INSERT INTO `sys_role` VALUES (103, '企业就业培训管理', '企业就业培训管理', 10, '1', 1, 1, '0', '0', 1, '2025-07-23 14:15:17', 1, '2025-07-23 23:06:12', NULL);
INSERT INTO `sys_role` VALUES (104, '个人培训申请', '个人培训申请', 12, '1', 1, 1, '0', '0', 1, '2025-07-23 14:15:42', 1, '2025-07-23 14:16:06', NULL);
INSERT INTO `sys_role` VALUES (105, '机构培训申请', '企业培训申请', 11, '1', 1, 1, '0', '0', 1, '2025-07-23 14:15:59', 1, '2025-07-23 22:38:55', NULL);
INSERT INTO `sys_role` VALUES (106, '零工市场', '零工市场', 20, '1', 1, 1, '0', '0', 1, '2025-07-23 22:56:21', 1, '2025-07-24 00:01:17', NULL);

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint(0) NOT NULL COMMENT '角色ID',
  `dept_id` bigint(0) NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色和部门关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint(0) NOT NULL COMMENT '角色ID',
  `menu_id` bigint(0) NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (1, 5000);
INSERT INTO `sys_role_menu` VALUES (1, 5001);
INSERT INTO `sys_role_menu` VALUES (1, 5002);
INSERT INTO `sys_role_menu` VALUES (1, 5003);
INSERT INTO `sys_role_menu` VALUES (1, 5004);
INSERT INTO `sys_role_menu` VALUES (1, 5010);
INSERT INTO `sys_role_menu` VALUES (1, 5011);
INSERT INTO `sys_role_menu` VALUES (1, 5012);
INSERT INTO `sys_role_menu` VALUES (1, 5013);
INSERT INTO `sys_role_menu` VALUES (1, 5014);
INSERT INTO `sys_role_menu` VALUES (1, 5015);
INSERT INTO `sys_role_menu` VALUES (1, 5016);
INSERT INTO `sys_role_menu` VALUES (1, 5017);
INSERT INTO `sys_role_menu` VALUES (1, 5018);
INSERT INTO `sys_role_menu` VALUES (1, 5020);
INSERT INTO `sys_role_menu` VALUES (1, 5021);
INSERT INTO `sys_role_menu` VALUES (1, 5022);
INSERT INTO `sys_role_menu` VALUES (1, 5023);
INSERT INTO `sys_role_menu` VALUES (1, 5024);
INSERT INTO `sys_role_menu` VALUES (1, 5025);
INSERT INTO `sys_role_menu` VALUES (1, 5026);
INSERT INTO `sys_role_menu` VALUES (1, 5027);
INSERT INTO `sys_role_menu` VALUES (1, 5028);
INSERT INTO `sys_role_menu` VALUES (1, 5029);
INSERT INTO `sys_role_menu` VALUES (1, 5030);
INSERT INTO `sys_role_menu` VALUES (1, 5031);
INSERT INTO `sys_role_menu` VALUES (1, 5040);
INSERT INTO `sys_role_menu` VALUES (1, 5041);
INSERT INTO `sys_role_menu` VALUES (1, 5042);
INSERT INTO `sys_role_menu` VALUES (1, 5043);
INSERT INTO `sys_role_menu` VALUES (1, 5050);
INSERT INTO `sys_role_menu` VALUES (1, 5051);
INSERT INTO `sys_role_menu` VALUES (1, 5052);
INSERT INTO `sys_role_menu` VALUES (1, 5053);
INSERT INTO `sys_role_menu` VALUES (1, 5054);
INSERT INTO `sys_role_menu` VALUES (1, 5055);
INSERT INTO `sys_role_menu` VALUES (1, 5056);
INSERT INTO `sys_role_menu` VALUES (1, 5057);
INSERT INTO `sys_role_menu` VALUES (1, 5058);
INSERT INTO `sys_role_menu` VALUES (100, 3000);
INSERT INTO `sys_role_menu` VALUES (100, 3001);
INSERT INTO `sys_role_menu` VALUES (100, 3002);
INSERT INTO `sys_role_menu` VALUES (100, 3010);
INSERT INTO `sys_role_menu` VALUES (100, 3011);
INSERT INTO `sys_role_menu` VALUES (100, 3012);
INSERT INTO `sys_role_menu` VALUES (100, 3013);
INSERT INTO `sys_role_menu` VALUES (100, 3014);
INSERT INTO `sys_role_menu` VALUES (100, 4045);
INSERT INTO `sys_role_menu` VALUES (101, 3000);
INSERT INTO `sys_role_menu` VALUES (101, 3001);
INSERT INTO `sys_role_menu` VALUES (101, 3002);
INSERT INTO `sys_role_menu` VALUES (101, 3010);
INSERT INTO `sys_role_menu` VALUES (101, 3011);
INSERT INTO `sys_role_menu` VALUES (101, 3012);
INSERT INTO `sys_role_menu` VALUES (101, 3013);
INSERT INTO `sys_role_menu` VALUES (101, 3014);
INSERT INTO `sys_role_menu` VALUES (101, 4046);
INSERT INTO `sys_role_menu` VALUES (102, 4047);
INSERT INTO `sys_role_menu` VALUES (103, 3003);
INSERT INTO `sys_role_menu` VALUES (103, 3030);
INSERT INTO `sys_role_menu` VALUES (103, 3031);
INSERT INTO `sys_role_menu` VALUES (103, 3032);
INSERT INTO `sys_role_menu` VALUES (103, 3033);
INSERT INTO `sys_role_menu` VALUES (103, 3034);
INSERT INTO `sys_role_menu` VALUES (103, 3035);
INSERT INTO `sys_role_menu` VALUES (103, 3036);
INSERT INTO `sys_role_menu` VALUES (103, 4048);
INSERT INTO `sys_role_menu` VALUES (103, 5060);
INSERT INTO `sys_role_menu` VALUES (103, 5061);
INSERT INTO `sys_role_menu` VALUES (103, 5062);
INSERT INTO `sys_role_menu` VALUES (103, 5063);
INSERT INTO `sys_role_menu` VALUES (103, 5064);
INSERT INTO `sys_role_menu` VALUES (103, 5065);
INSERT INTO `sys_role_menu` VALUES (103, 5066);
INSERT INTO `sys_role_menu` VALUES (103, 5067);
INSERT INTO `sys_role_menu` VALUES (103, 5070);
INSERT INTO `sys_role_menu` VALUES (103, 5071);
INSERT INTO `sys_role_menu` VALUES (103, 5072);
INSERT INTO `sys_role_menu` VALUES (103, 5073);
INSERT INTO `sys_role_menu` VALUES (103, 5074);
INSERT INTO `sys_role_menu` VALUES (103, 5075);
INSERT INTO `sys_role_menu` VALUES (103, 5076);
INSERT INTO `sys_role_menu` VALUES (103, 5077);
INSERT INTO `sys_role_menu` VALUES (104, 5059);
INSERT INTO `sys_role_menu` VALUES (105, 5069);
INSERT INTO `sys_role_menu` VALUES (106, 4001);
INSERT INTO `sys_role_menu` VALUES (106, 4002);
INSERT INTO `sys_role_menu` VALUES (106, 4101);
INSERT INTO `sys_role_menu` VALUES (106, 4102);
INSERT INTO `sys_role_menu` VALUES (106, 4103);
INSERT INTO `sys_role_menu` VALUES (106, 4104);
INSERT INTO `sys_role_menu` VALUES (106, 4105);
INSERT INTO `sys_role_menu` VALUES (106, 4106);
INSERT INTO `sys_role_menu` VALUES (106, 4107);
INSERT INTO `sys_role_menu` VALUES (106, 4108);
INSERT INTO `sys_role_menu` VALUES (106, 4201);
INSERT INTO `sys_role_menu` VALUES (106, 4202);
INSERT INTO `sys_role_menu` VALUES (106, 4203);
INSERT INTO `sys_role_menu` VALUES (106, 4204);
INSERT INTO `sys_role_menu` VALUES (106, 4205);
INSERT INTO `sys_role_menu` VALUES (106, 4206);
INSERT INTO `sys_role_menu` VALUES (106, 4207);
INSERT INTO `sys_role_menu` VALUES (106, 5000);
INSERT INTO `sys_role_menu` VALUES (106, 5001);
INSERT INTO `sys_role_menu` VALUES (106, 5002);
INSERT INTO `sys_role_menu` VALUES (106, 5010);
INSERT INTO `sys_role_menu` VALUES (106, 5011);
INSERT INTO `sys_role_menu` VALUES (106, 5012);
INSERT INTO `sys_role_menu` VALUES (106, 5013);
INSERT INTO `sys_role_menu` VALUES (106, 5014);
INSERT INTO `sys_role_menu` VALUES (106, 5015);
INSERT INTO `sys_role_menu` VALUES (106, 5016);
INSERT INTO `sys_role_menu` VALUES (106, 5017);
INSERT INTO `sys_role_menu` VALUES (106, 5018);
INSERT INTO `sys_role_menu` VALUES (106, 5020);
INSERT INTO `sys_role_menu` VALUES (106, 5021);
INSERT INTO `sys_role_menu` VALUES (106, 5022);
INSERT INTO `sys_role_menu` VALUES (106, 5023);
INSERT INTO `sys_role_menu` VALUES (106, 5024);
INSERT INTO `sys_role_menu` VALUES (106, 5025);
INSERT INTO `sys_role_menu` VALUES (106, 5026);
INSERT INTO `sys_role_menu` VALUES (106, 5027);
INSERT INTO `sys_role_menu` VALUES (106, 5028);
INSERT INTO `sys_role_menu` VALUES (106, 5029);
INSERT INTO `sys_role_menu` VALUES (106, 5030);
INSERT INTO `sys_role_menu` VALUES (106, 5031);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint(0) NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '账号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime(0) NULL DEFAULT NULL COMMENT '最后登录时间',
  `pwd_update_date` datetime(0) NULL DEFAULT NULL COMMENT '密码最后更新时间',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 104 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 103, 'admin', '若依', '00', '<EMAIL>', '15888888888', '1', '/gitData/work/文件/avatar/2025/07/11/阿摩司公爵_20250711222932A001.png', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2025-07-23 21:55:52', '2025-06-07 19:23:02', 1, '2025-06-07 19:23:02', NULL, '2025-07-23 21:55:52', '管理员');
INSERT INTO `sys_user` VALUES (2, 105, 'ry', '若依', '00', '<EMAIL>', '15666666666', '1', '', '$2a$10$Pk5tMzuw1PvtSl0OSl8UMOP.wMZCV6x1mXeP8bBEC9HSDnGsSP8oO', '0', '0', '127.0.0.1', '2025-06-07 19:23:02', '2025-06-07 19:23:02', 1, '2025-06-07 19:23:02', 1, '2025-07-24 00:02:57', '测试员');
INSERT INTO `sys_user` VALUES (100, NULL, 'admin_1', 'admin_1', '00', '', '', '0', '', '$2a$10$76GMn8TdW8siQaGK9WwoW.wC3cRwhpCrbY42tvCDHmZXGju/rPsgy', '0', '0', '127.0.0.1', '2025-07-23 00:05:40', '2025-07-23 00:05:48', 1, '2025-07-23 00:03:41', 1, '2025-07-24 00:03:03', NULL);
INSERT INTO `sys_user` VALUES (101, NULL, 'admin_2', 'admin_2', '00', '', '', '0', '', '$2a$10$boOvhwAMak4.UZTVo13Gd.tGgTbHckjHIN91hTLjAhtTfJt6HaeAu', '0', '0', '127.0.0.1', '2025-07-23 00:09:07', '2025-07-23 00:09:15', 1, '2025-07-23 00:03:55', 1, '2025-07-24 00:03:05', NULL);
INSERT INTO `sys_user` VALUES (102, NULL, 'admin_3', 'admin_3', '00', '', '', '0', '', '$2a$10$HsF6Cy0drv8JVuV.kmpQN.erhkEDvM61P8IbBzDIQgEoAl21FEmra', '0', '0', '127.0.0.1', '2025-07-23 00:09:46', '2025-07-23 00:04:28', 1, '2025-07-23 00:04:03', 1, '2025-07-24 00:03:09', NULL);
INSERT INTO `sys_user` VALUES (103, NULL, '个人培训申请', '个人培训申请', '00', '', '', '0', '', '$2a$10$VOBuN1FKWN6Ait3tfIR.3.Vq3TVt9UfMohGi6FZyo3x/kNVbyhLRG', '0', '0', '127.0.0.1', '2025-07-23 18:47:09', '2025-07-23 18:47:23', 1, '2025-07-23 18:46:54', 1, '2025-07-24 00:03:12', NULL);
INSERT INTO `sys_user` VALUES (104, NULL, '机构培训申请', '机构培训申请', '00', '', '', '0', '', '$2a$10$Vg267ZLqI1RuZLztiNShb.JKlfl54e.NIqyIhnuqFaojkFz1NFM8q', '0', '0', '127.0.0.1', '2025-07-23 22:38:35', NULL, 1, '2025-07-23 22:37:45', 1, '2025-07-24 00:03:16', NULL);
INSERT INTO `sys_user` VALUES (105, NULL, '零工市场', '零工市场', '00', '', '', '0', '', '$2a$10$CSlriGLo8YOdS1dEkncxQOZ1l.oVRFXOJjd92/yznRpgorluKcRgO', '0', '0', '127.0.0.1', '2025-07-24 00:02:33', '2025-07-23 22:57:12', 1, '2025-07-23 22:56:42', 1, '2025-07-24 00:03:18', NULL);
INSERT INTO `sys_user` VALUES (106, NULL, '企业就业培训管理', '企业就业培训管理', '00', '', '', '0', '', '$2a$10$eqFgViGQ5rEWPDeFn0QY5OYhtniQYudwaYwRkaaHx.gTJLhIY22t.', '0', '0', '127.0.0.1', '2025-07-23 23:05:42', '2025-07-23 23:05:52', 1, '2025-07-23 23:05:26', 1, '2025-07-24 00:03:20', NULL);

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `post_id` bigint(0) NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------
INSERT INTO `sys_user_post` VALUES (1, 1);

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `role_id` bigint(0) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户和角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1);
INSERT INTO `sys_user_role` VALUES (100, 100);
INSERT INTO `sys_user_role` VALUES (101, 101);
INSERT INTO `sys_user_role` VALUES (102, 102);
INSERT INTO `sys_user_role` VALUES (103, 104);
INSERT INTO `sys_user_role` VALUES (104, 105);
INSERT INTO `sys_user_role` VALUES (105, 106);
INSERT INTO `sys_user_role` VALUES (106, 103);

-- ----------------------------
-- Table structure for training_application
-- ----------------------------
DROP TABLE IF EXISTS `training_application`;
CREATE TABLE `training_application`  (
  `application_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '报名ID',
  `order_id` bigint(0) NOT NULL COMMENT '培训订单ID',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '用户ID',
  `applicant_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '报名人姓名',
  `applicant_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '报名人手机号',
  `applicant_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报名人邮箱',
  `applicant_id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报名人身份证号',
  `applicant_gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报名人性别',
  `applicant_age` int(0) NULL DEFAULT NULL COMMENT '报名人年龄',
  `applicant_education` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报名人学历',
  `applicant_experience` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '报名人工作经验',
  `applicant_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报名人地址',
  `application_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '报名状态（0待审核 1已通过 2已拒绝 3已取消）',
  `application_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '报名时间',
  `review_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `reviewer` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核人',
  `review_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核意见',
  `application_note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报名备注',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`application_id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_applicant_phone`(`applicant_phone`) USING BTREE,
  INDEX `idx_application_status`(`application_status`) USING BTREE,
  INDEX `idx_application_time`(`application_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '培训报名表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of training_application
-- ----------------------------
INSERT INTO `training_application` VALUES (1, 1, 1, '张三', '13800138001', '<EMAIL>', '110101199001011234', '男', 25, '本科', '有3年Java开发经验，熟悉Spring框架', '北京市朝阳区', '1', '2025-07-20 10:00:00', '2025-07-20 14:00:00', '李老师', '符合报名条件，通过审核', '希望能参加此次培训', NULL, '2025-07-20 10:00:00', NULL, '2025-07-20 14:00:00', NULL, '0');
INSERT INTO `training_application` VALUES (2, 1, 2, '李四', '13800138002', '<EMAIL>', '110101199002021234', '女', 28, '硕士', '有5年软件开发经验，精通多种编程语言', '北京市海淀区', '0', '2025-07-21 09:30:00', NULL, NULL, NULL, '对Java高级开发很感兴趣', NULL, '2025-07-21 09:30:00', NULL, '2025-07-21 09:30:00', NULL, '0');
INSERT INTO `training_application` VALUES (3, 2, 3, '王五', '13800138003', '<EMAIL>', '110101199003031234', '男', 30, '大专', '有2年数据分析经验，熟悉Excel和SQL', '北京市西城区', '1', '2025-07-21 11:00:00', '2025-07-21 15:30:00', '张老师', '基础扎实，适合参加培训', '想转行做数据分析师', NULL, '2025-07-21 11:00:00', NULL, '2025-07-23 23:07:20', NULL, '0');
INSERT INTO `training_application` VALUES (4, 8, 1, '你好', '15454545454', '', '', '', NULL, '', '', '', '3', '2025-07-23 18:36:29', NULL, NULL, NULL, '', 1, '2025-07-23 18:36:29', NULL, '2025-07-23 18:36:33', NULL, '0');
INSERT INTO `training_application` VALUES (5, 8, 1, '你好', '15454545454', '', '', '', NULL, '', '', '', '1', '2025-07-23 18:36:37', '2025-07-23 18:43:48', 'admin', '测试', '', 1, '2025-07-23 18:36:37', NULL, '2025-07-23 23:07:01', NULL, '0');
INSERT INTO `training_application` VALUES (6, 7, 1, '12321', '17574857483', '', '', '', NULL, '小学', '', '', '3', '2025-07-23 19:24:52', NULL, NULL, NULL, '', 1, '2025-07-23 19:24:52', NULL, '2025-07-23 19:24:57', NULL, '0');
INSERT INTO `training_application` VALUES (7, 7, 1, '12321', '17574857483', '', '', '', NULL, '小学', '', '', '0', '2025-07-23 19:24:59', NULL, NULL, NULL, '', 1, '2025-07-23 19:24:59', NULL, '2025-07-23 23:06:33', NULL, '0');

-- ----------------------------
-- Table structure for training_institution_application
-- ----------------------------
DROP TABLE IF EXISTS `training_institution_application`;
CREATE TABLE `training_institution_application`  (
  `application_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `order_id` bigint(0) NOT NULL COMMENT '培训订单ID',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '用户ID',
  `institution_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '机构名称',
  `institution_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '机构代码/统一社会信用代码',
  `legal_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '法定代表人',
  `contact_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `institution_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '机构地址',
  `institution_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '机构类型（企业/事业单位/社会组织等）',
  `established_date` date NULL DEFAULT NULL COMMENT '成立时间',
  `registered_capital` decimal(15, 2) NULL DEFAULT NULL COMMENT '注册资本（万元）',
  `business_scope` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '经营范围',
  `training_experience` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '培训经验描述',
  `training_capacity` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '培训能力描述',
  `training_plan` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '培训计划',
  `teacher_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '师资信息',
  `facility_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '设施设备信息',
  `qualification_files` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '资质文件路径（JSON格式存储多个文件）',
  `training_plan_file` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '培训计划文件路径',
  `teacher_cert_files` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '师资证明文件路径（JSON格式存储多个文件）',
  `facility_files` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '设施设备文件路径（JSON格式存储多个文件）',
  `other_files` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '其他材料文件路径（JSON格式存储多个文件）',
  `application_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '申请状态（0待审核 1已通过 2已拒绝 3已取消）',
  `application_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '申请时间',
  `review_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `reviewer` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核人',
  `review_comment` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核意见',
  `application_note` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '申请备注',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`application_id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_institution_name`(`institution_name`) USING BTREE,
  INDEX `idx_contact_phone`(`contact_phone`) USING BTREE,
  INDEX `idx_application_status`(`application_status`) USING BTREE,
  INDEX `idx_application_time`(`application_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '培训机构申请表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of training_institution_application
-- ----------------------------
INSERT INTO `training_institution_application` VALUES (1, 1, 1, '北京优秀培训机构', '91110000123456789X', '张总', '李经理', '13800138001', '<EMAIL>', '北京市朝阳区建国路88号', '企业', '2020-01-15', 500.00, '教育培训、技能培训、管理咨询', '拥有5年Java培训经验，累计培训学员超过1000人', '具备完善的Java培训体系，拥有资深讲师团队', '针对Java高级开发的系统性培训计划，包含理论学习和实践项目', '拥有10名资深Java讲师，平均从业经验8年以上', '配备现代化机房50间，每间可容纳30人同时上课', '[\"qualification1.pdf\",\"qualification2.pdf\"]', 'training_plan_java.pdf', '[\"teacher_cert1.pdf\",\"teacher_cert2.pdf\"]', '[\"facility1.jpg\",\"facility2.jpg\"]', '[\"other1.pdf\"]', '1', '2025-07-20 09:00:00', '2025-07-20 16:00:00', '审核员A', '机构资质齐全，培训能力强，通过审核', '希望能承接Java高级开发培训项目', 1, '2025-07-20 09:00:00', 1, '2025-07-20 16:00:00', NULL, '0');
INSERT INTO `training_institution_application` VALUES (2, 2, 2, '上海专业技能培训中心', '91310000987654321A', '王主任', '赵老师', '13800138002', '<EMAIL>', '上海市浦东新区张江高科技园区', '事业单位', '2018-06-01', 1000.00, '职业技能培训、认证考试、企业内训', '专注数据分析培训3年，与多家知名企业合作', '具备数据分析全栈培训能力，从基础到高级应用', '数据分析师培训计划，涵盖统计学、Python、机器学习等', '拥有8名数据分析专家，均具有实际项目经验', '配备高性能计算集群和专业数据分析软件', '[\"qualification3.pdf\",\"qualification4.pdf\"]', 'training_plan_data.pdf', '[\"teacher_cert3.pdf\",\"teacher_cert4.pdf\"]', '[\"facility3.jpg\",\"facility4.jpg\"]', '[\"other2.pdf\"]', '0', '2025-07-21 10:00:00', NULL, NULL, NULL, '申请承接数据分析培训项目', 1, '2025-07-21 10:00:00', 1, '2025-07-21 10:00:00', NULL, '0');
INSERT INTO `training_institution_application` VALUES (3, 8, 1, 'jishi ', '', '321', '理你', '***********', '', '421423432', '', '2025-06-30', NULL, '', '321', '321', '321', '321', '231', '[{\"name\":\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723225003A001.png\",\"fileName\":\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723225003A001.png\"}]', '[{\"name\":\"1_20250723222256A002.png\",\"fileName\":\"1_20250723222256A002.png\"}]', '[{\"name\":\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723222258A003.png\",\"fileName\":\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723222258A003.png\"}]', '[{\"name\":\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723222300A004.png\",\"fileName\":\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723222300A004.png\"}]', NULL, '1', '2025-07-23 22:23:25', '2025-07-23 22:25:02', 'admin', 'ces ', '', 1, '2025-07-23 22:23:25', 1, '2025-07-23 22:50:06', NULL, '0');
INSERT INTO `training_institution_application` VALUES (4, 7, 1, '测试机构', '', '15435413', '53', '***********', '', '32143242', '事业单位', NULL, NULL, '', '5343', '43', '4', '343', '', '[{\"name\":\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\",\"fileName\":\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\",\"sourceFileName\":\"【哲风壁纸】可爱-可爱猫-大眼猫.png\",\"url\":\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\",\"filePath\":\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230116A001.png\"}]', '[{\"name\":\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\",\"fileName\":\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\",\"sourceFileName\":\"【哲风壁纸】可爱-可爱猫-大眼猫.png\",\"url\":\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\",\"filePath\":\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230121A002.png\"}]', '[{\"name\":\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\",\"fileName\":\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\",\"sourceFileName\":\"【哲风壁纸】可爱-可爱猫-大眼猫.png\",\"url\":\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\",\"filePath\":\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230124A003.png\"}]', '[{\"name\":\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\",\"fileName\":\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\",\"sourceFileName\":\"【哲风壁纸】可爱-可爱猫-大眼猫.png\",\"url\":\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\",\"filePath\":\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723230127A004.png\"}]', NULL, '0', '2025-07-23 23:16:30', NULL, NULL, NULL, '', 1, '2025-07-23 23:01:29', 1, '2025-07-23 23:16:30', NULL, '0');

-- ----------------------------
-- Table structure for training_order
-- ----------------------------
DROP TABLE IF EXISTS `training_order`;
CREATE TABLE `training_order`  (
  `order_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '订单标题',
  `order_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '订单描述',
  `training_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '培训类型',
  `training_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '培训分类',
  `training_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '培训级别',
  `training_duration` int(0) NULL DEFAULT NULL COMMENT '培训时长(小时)',
  `max_participants` int(0) NULL DEFAULT NULL COMMENT '最大参与人数',
  `current_participants` int(0) NULL DEFAULT 0 COMMENT '当前报名人数',
  `training_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT '培训费用',
  `training_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '培训地址',
  `contact_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `start_date` datetime(0) NULL DEFAULT NULL COMMENT '开始时间',
  `end_date` datetime(0) NULL DEFAULT NULL COMMENT '结束时间',
  `registration_deadline` datetime(0) NULL DEFAULT NULL COMMENT '报名截止时间',
  `order_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '订单状态（0草稿 1发布 2进行中 3已完成 4已取消）',
  `is_featured` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '是否推荐（0否 1是）',
  `requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '报名要求',
  `certificate_info` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '证书信息',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`order_id`) USING BTREE,
  INDEX `idx_training_type`(`training_type`) USING BTREE,
  INDEX `idx_order_status`(`order_status`) USING BTREE,
  INDEX `idx_start_date`(`start_date`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '培训订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of training_order
-- ----------------------------
INSERT INTO `training_order` VALUES (1, 'Java高级开发工程师培训', '面向有一定Java基础的开发人员，深入学习Spring Boot、微服务架构、分布式系统等高级技术', '技术培训', 'IT技能', '高级', 120, 30, 0, 5800.00, '青岛市市南区香港中路10号颐和国际A座15楼', '张老师', '13800138001', '<EMAIL>', '2025-08-01 09:00:00', '2025-08-15 18:00:00', '2025-07-28 23:59:59', '1', '1', '要求具备2年以上Java开发经验，熟悉Spring框架基础知识', '完成培训可获得高级Java开发工程师认证证书', '0', 1, '2025-07-22 10:00:00', NULL, '2025-07-22 10:00:00', '企业内训项目');
INSERT INTO `training_order` VALUES (2, 'Python数据分析师培训', '零基础学习Python数据分析，包括pandas、numpy、matplotlib等核心库的使用', '技术培训', 'IT技能', '初级', 80, 25, 0, 3800.00, '青岛市崂山区海尔路178号', '李老师', '13800138002', '<EMAIL>', '2025-08-05 09:00:00', '2025-08-12 18:00:00', '2025-08-02 23:59:59', '1', '0', '无编程基础要求，适合零基础学员', '完成培训可获得Python数据分析师认证证书', '0', 1, '2025-07-22 10:30:00', NULL, '2025-07-22 10:30:00', '面向社会招生');
INSERT INTO `training_order` VALUES (3, '企业管理与领导力提升', '针对中高层管理人员的综合管理能力提升培训，涵盖团队管理、战略规划、沟通技巧等', '管理培训', '领导力', '中级', 40, 20, 12, 4500.00, '青岛市市北区辽宁路263号', '王老师', '13800138003', '<EMAIL>', '2025-07-30 09:00:00', '2025-08-01 18:00:00', '2025-07-25 23:59:59', '1', '1', '具备3年以上管理经验或相关工作背景', '完成培训可获得企业管理师认证证书', '0', 1, '2025-07-22 11:00:00', NULL, '2025-07-22 11:00:00', '管理技能提升');
INSERT INTO `training_order` VALUES (4, '数字营销与电商运营', '全面学习数字营销策略、社交媒体运营、电商平台操作等现代营销技能', '职业技能', '市场营销', '中级', 60, 35, 22, 0.00, '青岛市黄岛区长江中路216号', '刘老师', '13800138004', '<EMAIL>', '2025-08-10 09:00:00', '2025-08-17 18:00:00', '2025-08-05 23:59:59', '1', '0', '对电商或营销有基础了解，有相关工作经验优先', '完成培训可获得数字营销师认证证书', '0', 1, '2025-07-22 11:30:00', NULL, '2025-07-22 11:30:00', '免费公益培训');
INSERT INTO `training_order` VALUES (5, '前端开发技能提升', '学习最新的前端开发技术，包括Vue3、React、TypeScript等现代前端框架', '技术培训', 'IT技能', '中级', 100, 28, 18, 4200.00, '青岛市市南区东海西路15号', '陈老师', '13800138005', '<EMAIL>', '2025-08-12 09:00:00', '2025-08-20 18:00:00', '2025-08-08 23:59:59', '1', '1', '具备HTML、CSS、JavaScript基础知识', '完成培训可获得前端开发工程师认证证书', '0', 1, '2025-07-22 12:00:00', NULL, '2025-07-22 12:00:00', '前端技术进阶');
INSERT INTO `training_order` VALUES (6, '项目管理PMP认证培训', '系统学习项目管理知识体系，准备PMP认证考试，提升项目管理专业能力', '管理培训', '项目管理', '高级', 80, 15, 8, 6800.00, '青岛市崂山区科技街36号', '赵老师', '13800138006', '<EMAIL>', '2025-08-15 09:00:00', '2025-08-25 18:00:00', '2025-08-10 23:59:59', '1', '1', '具备3年以上项目管理经验，大学本科以上学历', '完成培训可获得PMP认证培训证书', '0', 1, '2025-07-22 12:30:00', NULL, '2025-07-22 12:30:00', 'PMP认证考试培训');
INSERT INTO `training_order` VALUES (7, '财务分析与预算管理', '深入学习财务分析方法、预算编制与控制、成本管理等财务管理核心技能', '管理培训', '财务管理', '中级', 50, 25, 0, 3500.00, '青岛市市南区山东路9号', '孙老师', '13800138007', '<EMAIL>', '2025-08-18 09:00:00', '2025-08-22 18:00:00', '2025-08-12 23:59:59', '1', '0', '具备财务基础知识，有一定工作经验', '完成培训可获得财务分析师认证证书', '0', 1, '2025-07-22 13:00:00', NULL, '2025-07-22 13:00:00', '财务技能提升');
INSERT INTO `training_order` VALUES (8, '人工智能与机器学习入门', '零基础学习人工智能和机器学习基础知识，包括Python编程、数据处理、算法原理等', '技术培训', 'IT技能', '初级', 120, 40, 1, 5200.00, '青岛市高新区智力岛路1号', '周老师', '13800138008', '<EMAIL>', '2025-08-20 09:00:00', '2025-09-05 18:00:00', '2025-08-15 23:59:59', '1', '1', '理工科背景，具备基础数学知识', '完成培训可获得AI工程师入门认证证书', '0', 1, '2025-07-22 13:30:00', NULL, '2025-07-22 13:30:00', 'AI技术入门');

-- ----------------------------
-- Table structure for training_registration
-- ----------------------------
DROP TABLE IF EXISTS `training_registration`;
CREATE TABLE `training_registration`  (
  `registration_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '报名ID',
  `order_id` bigint(0) NOT NULL COMMENT '订单ID',
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `participant_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参与者姓名',
  `participant_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参与者手机号',
  `participant_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '参与者邮箱',
  `participant_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '参与者公司',
  `participant_position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '参与者职位',
  `registration_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '报名状态（0待审核 1已通过 2已拒绝 3已取消）',
  `registration_time` datetime(0) NULL DEFAULT NULL COMMENT '报名时间',
  `audit_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `audit_user_id` bigint(0) NULL DEFAULT NULL COMMENT '审核人ID',
  `audit_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核备注',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`registration_id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_registration_status`(`registration_status`) USING BTREE,
  INDEX `idx_registration_time`(`registration_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '培训报名表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of training_registration
-- ----------------------------

-- ----------------------------
-- Table structure for worker_info
-- ----------------------------
DROP TABLE IF EXISTS `worker_info`;
CREATE TABLE `worker_info`  (
  `worker_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '零工ID',
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '真实姓名',
  `gender` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '性别(0男 1女)',
  `birth_date` date NULL DEFAULT NULL COMMENT '出生日期',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身份证号',
  `phone_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号码',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `current_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '当前所在地',
  `work_location_preference` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '工作地点偏好(JSON格式)',
  `education_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学历水平',
  `work_experience_years` int(0) NULL DEFAULT 0 COMMENT '工作经验年数',
  `work_experience_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '工作经验描述',
  `expected_salary_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '期望最低薪资',
  `expected_salary_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '期望最高薪资',
  `salary_unit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'month' COMMENT '薪资单位(hour/day/month)',
  `work_type_preference` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工作类型偏好',
  `available_start_date` date NULL DEFAULT NULL COMMENT '可开始工作日期',
  `available_end_date` date NULL DEFAULT NULL COMMENT '可工作截止日期',
  `work_time_preference` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '工作时间偏好(JSON格式)',
  `self_introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '自我介绍',
  `avatar` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像',
  `resume_file` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '简历文件',
  `certificate_files` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '证书文件(JSON格式)',
  `is_available` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '是否可接单(0否 1是)',
  `rating_score` decimal(3, 2) NULL DEFAULT 5.00 COMMENT '评分(1-5分)',
  `completed_jobs` int(0) NULL DEFAULT 0 COMMENT '完成工作数量',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`worker_id`) USING BTREE,
  UNIQUE INDEX `uk_user_id`(`user_id`) USING BTREE,
  INDEX `idx_current_location`(`current_location`) USING BTREE,
  INDEX `idx_education_level`(`education_level`) USING BTREE,
  INDEX `idx_is_available`(`is_available`) USING BTREE,
  INDEX `idx_rating_score`(`rating_score`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '零工信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of worker_info
-- ----------------------------

-- ----------------------------
-- Table structure for worker_profile
-- ----------------------------
DROP TABLE IF EXISTS `worker_profile`;
CREATE TABLE `worker_profile`  (
  `worker_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '零工ID',
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '真实姓名',
  `nickname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
  `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '性别（male/female）',
  `age` int(0) NULL DEFAULT NULL COMMENT '年龄',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
  `current_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '当前所在地（保留但不作为主要匹配条件）',
  `education_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学历水平（不限/初中/高中/中专/大专/本科/硕士/博士）',
  `work_experience_years` int(0) NULL DEFAULT NULL COMMENT '工作经验年数',
  `work_categories` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '工作类别偏好（JSON数组：服务员/保洁/搬运工/销售/厨师助手/快递员/保安等）',
  `job_types_preferred` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '偏好工作类型（JSON数组：全职/兼职/临时工/小时工）',
  `skills` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '技能列表（JSON格式）',
  `salary_expectation_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '期望最低薪资',
  `salary_expectation_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '期望最高薪资',
  `salary_type_preference` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '薪资类型偏好（hourly/daily/monthly/piece）',
  `availability_start_date` date NULL DEFAULT NULL COMMENT '可开始工作日期',
  `work_days_per_week` int(0) NULL DEFAULT NULL COMMENT '每周可工作天数',
  `work_hours_per_day` int(0) NULL DEFAULT NULL COMMENT '每日可工作小时数',
  `profile_photo` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像照片URL',
  `self_introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '自我介绍',
  `rating_average` decimal(3, 2) NULL COMMENT '平均评分',
  `rating_count` int(0) NULL DEFAULT 0 COMMENT '评分次数',
  `completed_jobs` int(0) NULL DEFAULT 0 COMMENT '完成工作数量',
  `success_rate` decimal(5, 2) NULL COMMENT '成功率',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'active' COMMENT '状态（active/inactive/suspended/banned）',
  `is_verified` tinyint(1) NULL DEFAULT 0 COMMENT '是否已实名验证（0否 1是）',
  `last_active_time` datetime(0) NULL DEFAULT NULL COMMENT '最后活跃时间',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`worker_id`) USING BTREE,
  UNIQUE INDEX `uk_user_id`(`user_id`) USING BTREE,
  INDEX `idx_education_level`(`education_level`) USING BTREE,
  INDEX `idx_salary_type`(`salary_type_preference`) USING BTREE,
  INDEX `idx_salary_expectation`(`salary_expectation_min`, `salary_expectation_max`) USING BTREE,
  INDEX `idx_work_experience`(`work_experience_years`) USING BTREE,
  INDEX `idx_match_core`(`education_level`, `salary_type_preference`, `status`) USING BTREE,
  INDEX `idx_match_salary`(`salary_type_preference`, `salary_expectation_min`, `salary_expectation_max`) USING BTREE,
  INDEX `idx_real_name`(`real_name`) USING BTREE,
  INDEX `idx_phone`(`phone`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_rating`(`rating_average`) USING BTREE,
  INDEX `idx_verified`(`is_verified`) USING BTREE,
  INDEX `idx_del_flag`(`del_flag`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '零工信息表（核心匹配优化版）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of worker_profile
-- ----------------------------
INSERT INTO `worker_profile` VALUES (1, 101, '张小美', '美美服务员', 'female', 22, '13900001001', '青岛市市南区', '高中', 2, '[\"服务员\"]', '[\"兼职\",\"临时工\"]', '[\"餐厅服务\",\"客户接待\",\"收银操作\",\"礼仪服务\"]', 20.00, 30.00, 'hourly', '2025-07-25', 6, 8, NULL, '有2年餐厅服务经验，服务态度好，沟通能力强，熟悉收银系统操作，形象气质佳', 4.80, 25, 18, 95.50, 'active', 1, '2025-07-23 22:12:33', 1, '2025-07-23 22:12:33', 105, '2025-07-23 23:03:52', '0', '优秀服务员，客户评价很高');
INSERT INTO `worker_profile` VALUES (2, 102, '李小雅', '雅雅', 'female', 20, '13900001002', '青岛市李沧区', '大专', 1, '[\"服务员\"]', '[\"兼职\"]', '[\"咖啡制作\",\"英语交流\",\"客户服务\",\"西餐服务\"]', 18.00, 25.00, 'hourly', '2025-07-25', 5, 6, NULL, '在校大学生，有咖啡厅兼职经验，英语四级，善于与客户沟通，学习能力强', 4.60, 15, 12, 92.30, 'active', 1, '2025-07-23 22:12:33', 1, '2025-07-23 22:12:33', NULL, '2025-07-23 22:12:33', '0', '大学生兼职，时间灵活');
INSERT INTO `worker_profile` VALUES (3, 103, '王小慧', '慧慧', 'female', 25, '13900001003', '青岛市崂山区', '本科', 3, '[\"服务员\"]', '[\"全职\",\"兼职\"]', '[\"高端服务\",\"礼仪接待\",\"多语言\",\"红酒知识\"]', 25.00, 35.00, 'hourly', '2025-08-01', 6, 8, NULL, '有星级酒店服务经验，形象气质佳，会英语和日语，服务意识强，擅长高端客户服务', 4.90, 32, 28, 97.80, 'suspended', 1, '2025-07-23 22:12:33', 1, '2025-07-23 22:12:33', 105, '2025-07-23 22:58:26', '0', '星级酒店经验，高端服务专家');
INSERT INTO `worker_profile` VALUES (4, 104, '刘大姐', '勤劳刘姐', 'female', 45, '13900001004', '青岛市市北区', '初中', 8, '[\"保洁\"]', '[\"全职\",\"兼职\",\"临时工\"]', '[\"家庭保洁\",\"办公室清洁\",\"深度清洁\",\"消毒杀菌\"]', 25.00, 35.00, 'hourly', '2025-07-25', 7, 8, NULL, '有8年保洁经验，工作认真负责，熟悉各种清洁用品使用，客户评价好，持有健康证', 4.70, 45, 38, 94.20, 'active', 1, '2025-07-23 22:12:33', 1, '2025-07-23 22:12:33', NULL, '2025-07-23 22:12:33', '0', '经验丰富的保洁阿姨');
INSERT INTO `worker_profile` VALUES (5, 105, '陈阿姨', '细心陈姨', 'female', 50, '13900001005', '青岛市城阳区', '初中', 10, '[\"保洁\"]', '[\"全职\",\"临时工\"]', '[\"家政服务\",\"整理收纳\",\"老人护理\",\"婴幼儿护理\"]', 120.00, 150.00, 'daily', '2025-07-25', 6, 8, NULL, '专业家政服务10年，擅长家庭整理收纳，有老人护理经验，持有健康证和护理证', 4.80, 56, 52, 96.40, 'active', 1, '2025-07-23 22:12:33', 1, '2025-07-23 22:12:33', NULL, '2025-07-23 22:12:33', '0', '专业家政服务，护理经验丰富');
INSERT INTO `worker_profile` VALUES (6, 106, '张大力', '力哥', 'male', 35, '13900001006', '青岛市黄岛区', '高中', 12, '[\"搬运工\"]', '[\"全职\",\"临时工\"]', '[\"重物搬运\",\"货物装卸\",\"仓库管理\",\"叉车操作\"]', 180.00, 220.00, 'daily', '2025-07-25', 6, 8, NULL, '有12年搬运工作经验，身体强壮，能吃苦耐劳，熟悉各种搬运设备操作，持有叉车证', 4.50, 67, 58, 91.80, 'active', 1, '2025-07-23 22:12:33', 1, '2025-07-23 22:12:33', NULL, '2025-07-23 22:12:33', '0', '力气大，经验丰富的搬运工');
INSERT INTO `worker_profile` VALUES (7, 107, '李小强', '强哥', 'male', 28, '13900001007', '青岛市即墨区', '中专', 5, '[\"搬运工\"]', '[\"兼职\",\"临时工\"]', '[\"家具搬运\",\"电器安装\",\"搬家服务\",\"包装打包\"]', 25.00, 35.00, 'hourly', '2025-07-25', 7, 8, NULL, '专业搬家服务5年，熟悉家具拆装，电器安装，服务态度好，无损坏记录，细心负责', 4.60, 34, 31, 93.50, 'active', 1, '2025-07-23 22:12:33', 1, '2025-07-23 22:12:33', NULL, '2025-07-23 22:12:33', '0', '搬家专家，零损坏记录');
INSERT INTO `worker_profile` VALUES (8, 108, '赵小销', '销售达人', 'male', 30, '13900001008', '青岛市市南区', '大专', 6, '[\"销售\"]', '[\"全职\"]', '[\"房产销售\",\"客户开发\",\"合同谈判\",\"市场分析\"]', 4000.00, 8000.00, 'monthly', '2025-08-01', 6, 8, NULL, '有6年房产销售经验，熟悉青岛房产市场，客户资源丰富，业绩优秀，沟通能力强', 4.70, 28, 24, 95.20, 'active', 1, '2025-07-23 22:12:33', 1, '2025-07-23 22:12:33', 105, '2025-07-23 22:58:19', '0', '房产销售精英，业绩突出');
INSERT INTO `worker_profile` VALUES (9, 109, '孙小促', '促销小能手', 'female', 24, '13900001009', '青岛市李沧区', '高中', 3, '[\"销售\"]', '[\"兼职\",\"临时工\"]', '[\"商品促销\",\"产品介绍\",\"客户服务\",\"活动策划\"]', 18.00, 25.00, 'hourly', '2025-07-25', 6, 6, NULL, '有3年促销经验，口才好，善于产品推介，熟悉各类商品特性，活泼开朗有亲和力', 4.40, 22, 19, 89.60, 'active', 1, '2025-07-23 22:12:33', 1, '2025-07-23 22:12:33', NULL, '2025-07-23 22:12:33', '0', '促销能手，亲和力强');
INSERT INTO `worker_profile` VALUES (10, 110, '马小厨', '厨房小马', 'male', 26, '13900001010', '青岛市市北区', '中专', 4, '[\"厨师助手\"]', '[\"全职\",\"兼职\"]', '[\"食材准备\",\"菜品制作\",\"厨房清洁\",\"中式烹饪\"]', 3000.00, 4000.00, 'monthly', '2025-08-01', 6, 10, NULL, '有4年厨房工作经验，熟悉中餐制作流程，刀工娴熟，工作效率高，持有健康证', 4.50, 18, 16, 92.70, 'active', 1, '2025-07-23 22:12:33', 1, '2025-07-23 22:12:33', NULL, '2025-07-23 22:12:33', '0', '厨房好手，刀工精湛');
INSERT INTO `worker_profile` VALUES (11, 111, '郑小快', '快递小郑', 'male', 27, '13900001011', '青岛市城阳区', '高中', 3, '[\"快递员\"]', '[\"全职\"]', '[\"快递配送\",\"路线规划\",\"客户服务\",\"电动车驾驶\"]', 4500.00, 6000.00, 'monthly', '2025-07-25', 6, 10, NULL, '有3年快递配送经验，熟悉青岛各区域路线，配送效率高，客户满意度好，有电动车驾照', 4.60, 89, 85, 94.30, 'active', 1, '2025-07-23 22:12:33', 1, '2025-07-23 22:12:33', NULL, '2025-07-23 22:12:33', '0', '配送达人，路线熟悉');
INSERT INTO `worker_profile` VALUES (12, 112, '田小安', '安全田哥', 'male', 40, '13900001012', '青岛市崂山区', '高中', 8, '[\"保安\"]', '[\"全职\"]', '[\"安全巡逻\",\"门岗值守\",\"应急处理\",\"监控操作\"]', 3200.00, 3800.00, 'monthly', '2025-08-01', 6, 12, NULL, '有8年保安工作经验，持有保安证，责任心强，熟悉安全管理制度，应急处理能力强', 4.70, 23, 21, 96.10, 'active', 1, '2025-07-23 22:12:33', 1, '2025-07-23 22:12:33', NULL, '2025-07-23 22:12:33', '0', '资深保安，责任心强');
INSERT INTO `worker_profile` VALUES (13, 113, '何小收', '收银小何', 'female', 23, '13900001013', '青岛市黄岛区', '高中', 2, '[\"收银员\"]', '[\"兼职\",\"临时工\"]', '[\"收银操作\",\"会员服务\",\"商品管理\",\"POS机操作\"]', 18.00, 22.00, 'hourly', '2025-07-25', 6, 6, NULL, '有2年收银工作经验，熟练操作各种收银系统，服务态度好，计算准确，工作细心', 4.50, 31, 28, 93.80, 'active', 1, '2025-07-23 22:12:33', 1, '2025-07-23 22:12:33', NULL, '2025-07-23 22:12:33', '0', '收银能手，准确高效');
INSERT INTO `worker_profile` VALUES (14, 1, '李某', NULL, 'female', NULL, '16545454545', '深圳', '初中', NULL, '搬运工', '全职', NULL, 1999.00, 49999.00, 'hourly', NULL, NULL, NULL, NULL, NULL, 0.00, 0, 0, 0.00, 'active', 1, '2025-07-23 22:14:41', 1, '2025-07-23 22:14:41', 1, '2025-07-23 22:14:52', '0', NULL);

-- ----------------------------
-- Table structure for worker_skill
-- ----------------------------
DROP TABLE IF EXISTS `worker_skill`;
CREATE TABLE `worker_skill`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `worker_id` bigint(0) NOT NULL COMMENT '零工ID',
  `skill_id` bigint(0) NOT NULL COMMENT '技能ID',
  `skill_level` tinyint(0) NULL DEFAULT 1 COMMENT '技能等级(1-初级 2-中级 3-高级)',
  `experience_years` int(0) NULL DEFAULT 0 COMMENT '该技能经验年数',
  `certificate_file` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '技能证书文件',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_worker_skill`(`worker_id`, `skill_id`) USING BTREE,
  INDEX `idx_worker_id`(`worker_id`) USING BTREE,
  INDEX `idx_skill_id`(`skill_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '零工技能关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of worker_skill
-- ----------------------------

-- ----------------------------
-- View structure for v_match_detail
-- ----------------------------
DROP VIEW IF EXISTS `v_match_detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_match_detail` AS select `m`.`match_id` AS `match_id`,`m`.`recruitment_id` AS `recruitment_id`,`m`.`worker_id` AS `worker_id`,`m`.`match_score` AS `match_score`,`m`.`skill_match_score` AS `skill_match_score`,`m`.`location_match_score` AS `location_match_score`,`m`.`salary_match_score` AS `salary_match_score`,`m`.`experience_match_score` AS `experience_match_score`,`m`.`education_match_score` AS `education_match_score`,`m`.`time_match_score` AS `time_match_score`,`m`.`match_details` AS `match_details`,`m`.`match_status` AS `match_status`,`m`.`is_mutual_match` AS `is_mutual_match`,`m`.`employer_viewed` AS `employer_viewed`,`m`.`worker_viewed` AS `worker_viewed`,`m`.`match_time` AS `match_time`,`m`.`create_id` AS `create_id`,`m`.`create_time` AS `create_time`,`m`.`update_id` AS `update_id`,`m`.`update_time` AS `update_time`,`m`.`remark` AS `remark`,`r`.`job_title` AS `job_title`,`r`.`company_name` AS `company_name`,`r`.`work_location` AS `job_location`,`r`.`salary_min` AS `salary_min`,`r`.`salary_max` AS `salary_max`,`r`.`salary_unit` AS `salary_unit`,`w`.`real_name` AS `worker_name`,`w`.`phone_number` AS `worker_phone`,`w`.`current_location` AS `worker_location`,`w`.`rating_score` AS `worker_rating`,`u1`.`nick_name` AS `employer_name`,`u2`.`nick_name` AS `worker_user_name` from ((((`match_record` `m` left join `recruitment_info` `r` on((`m`.`recruitment_id` = `r`.`recruitment_id`))) left join `worker_info` `w` on((`m`.`worker_id` = `w`.`worker_id`))) left join `sys_user` `u1` on((`r`.`publisher_user_id` = `u1`.`user_id`))) left join `sys_user` `u2` on((`w`.`user_id` = `u2`.`user_id`))) where ((`r`.`del_flag` = '0') and (`w`.`del_flag` = '0'));

-- ----------------------------
-- View structure for v_recruitment_detail
-- ----------------------------
DROP VIEW IF EXISTS `v_recruitment_detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_recruitment_detail` AS select `r`.`recruitment_id` AS `recruitment_id`,`r`.`job_title` AS `job_title`,`r`.`company_name` AS `company_name`,`r`.`job_description` AS `job_description`,`r`.`job_requirements` AS `job_requirements`,`r`.`work_location` AS `work_location`,`r`.`work_type` AS `work_type`,`r`.`salary_min` AS `salary_min`,`r`.`salary_max` AS `salary_max`,`r`.`salary_unit` AS `salary_unit`,`r`.`education_requirement` AS `education_requirement`,`r`.`experience_requirement` AS `experience_requirement`,`r`.`age_min` AS `age_min`,`r`.`age_max` AS `age_max`,`r`.`gender_requirement` AS `gender_requirement`,`r`.`contact_person` AS `contact_person`,`r`.`contact_phone` AS `contact_phone`,`r`.`contact_email` AS `contact_email`,`r`.`recruitment_count` AS `recruitment_count`,`r`.`current_applicants` AS `current_applicants`,`r`.`work_start_date` AS `work_start_date`,`r`.`work_end_date` AS `work_end_date`,`r`.`application_deadline` AS `application_deadline`,`r`.`is_urgent` AS `is_urgent`,`r`.`is_featured` AS `is_featured`,`r`.`status` AS `status`,`r`.`publisher_user_id` AS `publisher_user_id`,`r`.`company_logo` AS `company_logo`,`r`.`job_images` AS `job_images`,`r`.`del_flag` AS `del_flag`,`r`.`create_id` AS `create_id`,`r`.`create_time` AS `create_time`,`r`.`update_id` AS `update_id`,`r`.`update_time` AS `update_time`,`r`.`remark` AS `remark`,`u`.`nick_name` AS `publisher_name`,`u`.`phonenumber` AS `publisher_phone`,group_concat(concat(`st`.`skill_name`,'(',`rs`.`skill_level_required`,')') separator ', ') AS `required_skills` from (((`recruitment_info` `r` left join `sys_user` `u` on((`r`.`publisher_user_id` = `u`.`user_id`))) left join `recruitment_skill` `rs` on((`r`.`recruitment_id` = `rs`.`recruitment_id`))) left join `skill_tag` `st` on((`rs`.`skill_id` = `st`.`skill_id`))) where (`r`.`del_flag` = '0') group by `r`.`recruitment_id`;

-- ----------------------------
-- View structure for v_worker_detail
-- ----------------------------
DROP VIEW IF EXISTS `v_worker_detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_worker_detail` AS select `w`.`worker_id` AS `worker_id`,`w`.`user_id` AS `user_id`,`w`.`real_name` AS `real_name`,`w`.`gender` AS `gender`,`w`.`birth_date` AS `birth_date`,`w`.`id_card` AS `id_card`,`w`.`phone_number` AS `phone_number`,`w`.`email` AS `email`,`w`.`current_location` AS `current_location`,`w`.`work_location_preference` AS `work_location_preference`,`w`.`education_level` AS `education_level`,`w`.`work_experience_years` AS `work_experience_years`,`w`.`work_experience_desc` AS `work_experience_desc`,`w`.`expected_salary_min` AS `expected_salary_min`,`w`.`expected_salary_max` AS `expected_salary_max`,`w`.`salary_unit` AS `salary_unit`,`w`.`work_type_preference` AS `work_type_preference`,`w`.`available_start_date` AS `available_start_date`,`w`.`available_end_date` AS `available_end_date`,`w`.`work_time_preference` AS `work_time_preference`,`w`.`self_introduction` AS `self_introduction`,`w`.`avatar` AS `avatar`,`w`.`resume_file` AS `resume_file`,`w`.`certificate_files` AS `certificate_files`,`w`.`is_available` AS `is_available`,`w`.`rating_score` AS `rating_score`,`w`.`completed_jobs` AS `completed_jobs`,`w`.`status` AS `status`,`w`.`del_flag` AS `del_flag`,`w`.`create_id` AS `create_id`,`w`.`create_time` AS `create_time`,`w`.`update_id` AS `update_id`,`w`.`update_time` AS `update_time`,`w`.`remark` AS `remark`,`u`.`nick_name` AS `user_name`,`u`.`email` AS `user_email`,group_concat(concat(`st`.`skill_name`,'(',`ws`.`skill_level`,')') separator ', ') AS `worker_skills`,timestampdiff(YEAR,`w`.`birth_date`,curdate()) AS `age` from (((`worker_info` `w` left join `sys_user` `u` on((`w`.`user_id` = `u`.`user_id`))) left join `worker_skill` `ws` on((`w`.`worker_id` = `ws`.`worker_id`))) left join `skill_tag` `st` on((`ws`.`skill_id` = `st`.`skill_id`))) where (`w`.`del_flag` = '0') group by `w`.`worker_id`;

-- ----------------------------
-- Procedure structure for sp_calculate_match_score
-- ----------------------------
DROP PROCEDURE IF EXISTS `sp_calculate_match_score`;
delimiter ;;
CREATE PROCEDURE `sp_calculate_match_score`(IN p_recruitment_id BIGINT,
    IN p_worker_id BIGINT,
    OUT p_match_score DECIMAL(5,2))
BEGIN
    DECLARE v_skill_score DECIMAL(5,2) DEFAULT 0;
    DECLARE v_location_score DECIMAL(5,2) DEFAULT 0;
    DECLARE v_salary_score DECIMAL(5,2) DEFAULT 0;
    DECLARE v_experience_score DECIMAL(5,2) DEFAULT 0;
    DECLARE v_education_score DECIMAL(5,2) DEFAULT 0;
    DECLARE v_time_score DECIMAL(5,2) DEFAULT 0;

    -- 技能匹配计算 (权重40%)
    SELECT COALESCE(
        (SELECT AVG(
            CASE
                WHEN ws.skill_level >= rs.skill_level_required THEN 100
                WHEN ws.skill_level = rs.skill_level_required - 1 THEN 80
                ELSE 50
            END
        )
        FROM recruitment_skill rs
        LEFT JOIN worker_skill ws ON rs.skill_id = ws.skill_id AND ws.worker_id = p_worker_id
        WHERE rs.recruitment_id = p_recruitment_id), 0
    ) INTO v_skill_score;

    -- 地点匹配计算 (权重20%)
    SELECT CASE
        WHEN w.current_location LIKE CONCAT('%', SUBSTRING_INDEX(r.work_location, ' ', 1), '%') THEN 100
        WHEN w.current_location LIKE CONCAT('%', SUBSTRING_INDEX(r.work_location, ' ', 2), '%') THEN 80
        ELSE 30
    END INTO v_location_score
    FROM recruitment_info r, worker_info w
    WHERE r.recruitment_id = p_recruitment_id AND w.worker_id = p_worker_id;

    -- 薪资匹配计算 (权重15%)
    SELECT CASE
        WHEN w.expected_salary_min <= r.salary_max AND w.expected_salary_max >= r.salary_min THEN 100
        WHEN w.expected_salary_min <= r.salary_max * 1.2 THEN 80
        ELSE 40
    END INTO v_salary_score
    FROM recruitment_info r, worker_info w
    WHERE r.recruitment_id = p_recruitment_id AND w.worker_id = p_worker_id;

    -- 经验匹配计算 (权重15%)
    SELECT CASE
        WHEN w.work_experience_years >=
            CASE r.experience_requirement
                WHEN '1-3年' THEN 1
                WHEN '3-5年' THEN 3
                WHEN '5年以上' THEN 5
                ELSE 0
            END THEN 100
        ELSE 60
    END INTO v_experience_score
    FROM recruitment_info r, worker_info w
    WHERE r.recruitment_id = p_recruitment_id AND w.worker_id = p_worker_id;

    -- 学历匹配计算 (权重5%)
    SELECT CASE
        WHEN r.education_requirement IS NULL OR r.education_requirement = '' THEN 100
        WHEN w.education_level = r.education_requirement THEN 100
        ELSE 70
    END INTO v_education_score
    FROM recruitment_info r, worker_info w
    WHERE r.recruitment_id = p_recruitment_id AND w.worker_id = p_worker_id;

    -- 时间匹配计算 (权重5%)
    SELECT CASE
        WHEN w.available_start_date <= r.work_start_date
             AND (w.available_end_date IS NULL OR w.available_end_date >= r.work_end_date) THEN 100
        WHEN w.available_start_date <= r.work_start_date + INTERVAL 7 DAY THEN 80
        ELSE 50
    END INTO v_time_score
    FROM recruitment_info r, worker_info w
    WHERE r.recruitment_id = p_recruitment_id AND w.worker_id = p_worker_id;

    -- 计算总分
    SET p_match_score = (
        v_skill_score * 0.4 +
        v_location_score * 0.2 +
        v_salary_score * 0.15 +
        v_experience_score * 0.15 +
        v_education_score * 0.05 +
        v_time_score * 0.05
    );

    -- 插入或更新匹配记录
    INSERT INTO match_record (
        recruitment_id, worker_id, match_score,
        skill_match_score, location_match_score, salary_match_score,
        experience_match_score, education_match_score, time_match_score,
        match_time, create_time
    ) VALUES (
        p_recruitment_id, p_worker_id, p_match_score,
        v_skill_score, v_location_score, v_salary_score,
        v_experience_score, v_education_score, v_time_score,
        NOW(), NOW()
    ) ON DUPLICATE KEY UPDATE
        match_score = p_match_score,
        skill_match_score = v_skill_score,
        location_match_score = v_location_score,
        salary_match_score = v_salary_score,
        experience_match_score = v_experience_score,
        education_match_score = v_education_score,
        time_match_score = v_time_score,
        match_time = NOW(),
        update_time = NOW();

END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
