<template>
  <div v-if="(Object.keys(file).length > 0 && file.filePath) || onlyPreview || templateType"
    class="file-view-container">
    <div v-if="!onlyPreview" class="file-preview" @mouseenter="showActions = true" @mouseleave="showActions = false">
      <div v-if="isImageFile" class="file-thumbnail">
        <img :src="getFileFullUrl(file.filePath)" class="thumbnail-image" />
      </div>
      <div v-else-if="isExcelFile" class="file-thumbnail">
        <div class="file-icon excel-icon">
          <span>EXCEL</span>
        </div>
      </div>
      <div v-else-if="isWordFile" class="file-thumbnail">
        <div class="file-icon word-icon">
          <span>WORD</span>
        </div>
      </div>
      <div v-else-if="isPdfFile || fileType === 'PDF'" class="file-thumbnail">
        <div class="file-icon pdf-icon">
          <span>PDF</span>
        </div>
      </div>
      <div v-else class="file-thumbnail">
        <div class="file-icon document-icon">
          <span>文档</span>
        </div>
      </div>

      <!-- 悬浮操作按钮 -->
      <div v-if="showActions" class="file-actions">
        <div class="action-buttons">
          <template v-if="isImageFile">
            <el-button type="primary" size="small" @click.stop="previewImage">预览</el-button>
            <el-button type="success" size="small" @click.stop="downloadFile">下载</el-button>
          </template>
          <template v-else-if="isExcelFile || isWordFile || isPdfFile || fileType === 'PDF'">
            <el-button type="primary" size="small" @click.stop="previewOfficeFile">预览</el-button>
            <el-button type="success" size="small" @click.stop="downloadFile">下载</el-button>
          </template>
          <template v-else>
            <el-button type="success" size="small" @click.stop="downloadFile">下载</el-button>
          </template>
        </div>
      </div>
    </div>
    <div v-else="!onlyPreview" class="file-name-container">
      <el-tooltip class="box-item" effect="dark" :content="getFileName()" placement="top-start">
        <span class="file-name">{{ getFileName() }}</span>
      </el-tooltip>
    </div>

    <!-- 文件预览对话框 -->
    <Transition name="dialog-fade" appear>
      <div v-if="shouldShowDialog" class="custom-dialog-overlay" @click.self="closePreview">
        <div class="custom-dialog-container" :style="containerStyle">
          <div class="custom-dialog-header">
            <h3>{{ previewTitle || customTitle }}</h3>
            <div class="header-actions">
              <el-tooltip :content="isFullscreen ? '退出全屏' : '全屏'" placement="top" :hide-after="1500">
                <el-button type="text" class="action-btn" @click="toggleFullscreen">
                  <el-icon>
                    <FullScreen v-if="!isFullscreen" />
                    <svg v-else viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-78e17ca8=""
                      class="exit-fullscreen-icon">
                      <path fill="currentColor"
                        d="M160 96.064l192 .192a32 32 0 0 1 0 64l-192-.192V352a32 32 0 0 1-64 0V96h64v.064zm0 831.872V928H96V672a32 32 0 1 1 64 0v191.936l192-.192a32 32 0 1 1 0 64l-192 .192zM864 96.064V96h64v256a32 32 0 1 1-64 0V160.064l-192 .192a32 32 0 1 1 0-64l192-.192zm0 831.872-192-.192a32 32 0 0 1 0-64l192 .192V672a32 32 0 1 1 64 0v256h-64v-.064z">
                      </path>
                    </svg>
                  </el-icon>
                </el-button>
              </el-tooltip>
              <el-button type="text" @click="closePreview" class="close-btn">
                <el-icon>
                  <Close />
                </el-icon>
              </el-button>
            </div>
          </div>
          <div :style="contentStyle" class="content-wrapper">
            <div class="custom-dialog-body">
              <!-- 加载状态 -->
              <div v-if="isLoading" class="loading-template">
                <el-icon class="is-loading">
                  <Loading />
                </el-icon>
                <span>加载中...</span>
              </div>
              <!-- 根据文件类型使用不同的预览组件 -->
              <template v-else>
                <vue-office-pdf v-if="(isPdfFile || fileType === 'PDF') && currentPreviewUrl" :src="currentPreviewUrl"
                  :style="officeViewerStyle" />
                <vue-office-docx v-else-if="(isWordFile || fileType === 'WORD') && currentPreviewUrl"
                  :src="currentPreviewUrl" :style="officeViewerStyle" />
                <vue-office-excel v-else-if="(isExcelFile || fileType === 'EXCEL') && currentPreviewUrl"
                  :src="currentPreviewUrl" :style="officeViewerStyle" />
                <img v-else-if="(isImageFile || fileType === 'IMAGE') && currentPreviewUrl" :src="currentPreviewUrl"
                  class="image-preview" />
                <div v-else class="no-preview">
                  <el-icon class="no-preview-icon">
                    <Document />
                  </el-icon>
                  <span>暂无预览</span>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue';
import { FullScreen, Close, Loading, Document } from '@element-plus/icons-vue';
import VueOfficePdf from '@vue-office/pdf';
import VueOfficeDocx from '@vue-office/docx';
import VueOfficeExcel from '@vue-office/excel';
import { ElMessage } from 'element-plus';

const dirUrl = import.meta.env.VITE_APP_FILE_API;
const showActions = ref(false);

// 文件预览对话框相关变量
const previewDialogVisible = ref(false);
const previewUrl = ref('');
const previewTitle = ref('文件预览');
const isFullscreen = ref(false);
const currentPreviewUrl = ref('');
const isLoading = ref(false);

// 防抖标志
const isUpdating = ref(false);

// 定义emit
const emit = defineEmits(['close', 'update:visible', 'load-error', 'load-success']);

// 统一控制弹窗显示状态
const shouldShowDialog = computed(() => {
  return (previewDialogVisible.value || props.visible) && !isUpdating.value;
});

// 容器样式
const containerStyle = computed(() => {
  if (isFullscreen.value) {
    return {
      width: '100%',
      height: '100%',
      maxWidth: '100%',
      margin: 0,
      borderRadius: 0
    };
  }
  return {
    width: props.width || '80%'
  };
});

// 内容区域样式
const contentStyle = computed(() => {
  return {
    height: isFullscreen.value ? 'calc(100vh - 60px)' : '80vh',
    overflow: 'hidden'
  };
});

// Office查看器样式
const officeViewerStyle = computed(() => {
  return {
    width: '100%',
    height: '100%'
  };
});

const props = defineProps({
  file: {
    type: Object,
    default: () => {
      return {}
    }
  },
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '文件预览'
  },
  width: {
    type: [String, Number],
    default: '80%'
  },
  fileType: {
    type: String,
    default: ''
  },
  onlyPreview: {
    type: Boolean,
    default: false
  },
  urlPrefix: {
    type: String,
    default: '/office-file' // 默认前缀
  },
  // 新增的模板相关属性
  templateType: {
    type: String,
    default: '' // CHANNEL_COOPERATION, CHANNEL_INTERMEDIARY, CHANNEL_COURSE 等
  },
  templateApi: {
    type: Function,
    default: null // 获取模板的API函数
  },
  autoLoad: {
    type: Boolean,
    default: false // 是否自动加载模板
  }
});

// 自定义标题
const customTitle = computed(() => {
  return props.title;
});

// 获取文件完整URL
const getFileFullUrl = (filePath) => {
  if (!filePath) return '';

  // 如果filePath已经包含前缀，则直接返回
  if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
    return filePath;
  }

  // 如果filePath以/开头，直接拼接基础URL
  if (filePath.startsWith('/')) {
    return dirUrl + filePath;
  }

  // 否则添加/分隔符
  return dirUrl + (dirUrl.endsWith('/') ? '' : '/') + filePath;
};

// 图片类型后缀列表
const imageExtensions = new Set([
  'jpg', 'jpeg', 'png', 'gif', 'bmp',
  'webp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif'
]);

// Excel文件后缀列表
const excelExtensions = new Set([
  'xls', 'xlsx', 'csv', 'xlsm', 'xlsb'
]);

// Word文件后缀列表
const wordExtensions = new Set([
  'doc', 'docx', 'rtf', 'docm', 'dot', 'dotx'
]);

// PDF文件后缀
const pdfExtensions = new Set([
  'pdf'
]);

// 获取文件扩展名
const getFileExtension = () => {
  // 尝试从不同字段获取文件名
  let fileName = props.file.sourceFileName || props.file.fileName || props.file.name || props.file.filePath || props.file.url || '';

  // 如果是完整URL，先提取文件名部分
  if (fileName.includes('/')) {
    fileName = fileName.split('/').pop() || '';
  }

  // 如果是Windows路径，处理反斜杠
  if (fileName.includes('\\')) {
    fileName = fileName.split('\\').pop() || '';
  }

  // 移除URL参数
  if (fileName.includes('?')) {
    fileName = fileName.split('?')[0];
  }

  // 提取扩展名
  const parts = fileName.split('.');
  if (parts.length > 1) {
    return parts.pop()?.toLowerCase() || '';
  }

  return '';
};

// 计算属性判断是否是图片
const isImageFile = computed(() => {
  // 优先使用文件后缀名判断
  const extension = getFileExtension();
  if (extension) {
    return imageExtensions.has(extension);
  }

  // 如果没有文件路径，才使用fileType字段
  if (props.file.fileType) {
    return props.file.fileType === 'IMAGE';
  }

  return false;
});

// 计算属性判断是否是Excel文件
const isExcelFile = computed(() => {
  // 优先使用文件后缀名判断
  const extension = getFileExtension();
  if (extension) {
    return excelExtensions.has(extension);
  }

  // 如果没有文件路径，才使用fileType字段
  if (props.file.fileType) {
    return props.file.fileType === 'EXCEL';
  }

  return false;
});

// 计算属性判断是否是Word文件
const isWordFile = computed(() => {
  // 优先使用文件后缀名判断
  const extension = getFileExtension();
  if (extension) {
    return wordExtensions.has(extension);
  }

  // 如果没有文件路径，才使用fileType字段
  if (props.file.fileType) {
    return props.file.fileType === 'WORD';
  }

  return false;
});

// 计算属性判断是否是PDF文件
const isPdfFile = computed(() => {
  // 优先使用文件后缀名判断
  const extension = getFileExtension();
  if (extension) {
    return pdfExtensions.has(extension);
  }

  // 如果没有文件路径，才使用fileType字段
  if (props.file.fileType) {
    return props.file.fileType === 'PDF';
  }

  return false;
});

// 计算属性获取文件名
const getFileName = () => {
  // 如果是模板类型，返回模板名称
  if (props.templateType) {
    return getTemplateFileName(props.templateType);
  }

  // 优先使用sourceFileName字段
  if (props.file.sourceFileName) {
    return props.file.sourceFileName;
  }

  // 其次使用fileName字段
  if (props.file.fileName) {
    return props.file.fileName;
  }

  // 最后使用name字段
  if (props.file.name) {
    return props.file.name;
  }

  // 如果都没有，从filePath或url中提取文件名
  const filePath = props.file.filePath || props.file.url || '';
  if (filePath) {
    // 处理URL路径
    let fileName = filePath.split('/').pop() || '';
    // 处理Windows路径
    if (fileName.includes('\\')) {
      fileName = fileName.split('\\').pop() || '';
    }
    // 如果还是没有文件名，返回默认值
    return fileName || '未知文件';
  }

  return '未知文件';
};

// 初始化预览
const initPreview = () => {
  if (props.templateType) {
    // 如果是模板类型，不需要立即加载，等待用户点击预览按钮
    previewTitle.value = getTemplateFileName(props.templateType);
    return;
  }

  if (props.file && props.file.filePath) {
    const fileUrl = getFileFullUrl(props.file.filePath);
    currentPreviewUrl.value = fileUrl;
    previewTitle.value = getFileName();
  }
};

// 防抖函数
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 防抖的状态更新函数
const debouncedUpdateState = debounce(async (newVisible, newFile, newTemplateType) => {
  if (isUpdating.value) return;

  isUpdating.value = true;

  try {
    if (newVisible) {
      if (newTemplateType && props.templateApi && props.autoLoad) {
        // 如果是模板类型，并且需要自动加载，则直接加载模板
        await loadAndPreviewTemplate();
      } else if (newFile && newFile.filePath) {
        // 否则，如果有文件路径，就初始化预览
        initPreview();
        await nextTick();
        previewDialogVisible.value = true;
      }
    } else {
      // 如果设置为不可见，则关闭预览
      previewDialogVisible.value = false;
      currentPreviewUrl.value = '';
    }
  } finally {
    // 延迟重置更新标志，确保状态稳定
    setTimeout(() => {
      isUpdating.value = false;
    }, 100);
  }
}, 50);

// 监听file和visible变化
watch(() => [props.visible, props.file, props.templateType], ([newVisible, newFile, newTemplateType]) => {
  if ((newFile && newFile.filePath && (newVisible || props.onlyPreview)) || newTemplateType) {
    if (!newVisible && !props.onlyPreview) {
      initPreview();
    } else {
      debouncedUpdateState(newVisible, newFile, newTemplateType);
    }
  }
}, { deep: true, immediate: true });

// 加载并预览模板
const loadAndPreviewTemplate = async () => {
  if (!props.templateType || !props.templateApi || isUpdating.value) return;

  isLoading.value = true;
  try {
    const res = await props.templateApi(props.templateType);
    if (res.code === 200 && res.data) {
      // 构建完整的URL
      const fileUrl = props.urlPrefix + res.data;

      // 确保状态更新是原子性的
      await nextTick();
      currentPreviewUrl.value = fileUrl;
      previewTitle.value = props.title || '模板预览';

      // 延迟显示弹窗，确保内容已准备好
      setTimeout(() => {
        previewDialogVisible.value = true;
      }, 50);

      emit('load-success', res.data);
    } else {
      ElMessage.error(res.msg || '获取模板文件失败');
      emit('load-error', res.msg || '获取模板文件失败');
    }
  } catch (error) {
    console.error('获取模板路径失败', error);
    ElMessage.error('获取模板文件失败');
    emit('load-error', '获取模板文件失败');
  } finally {
    // 延迟重置加载状态，避免闪动
    setTimeout(() => {
      isLoading.value = false;
    }, 100);
  }
};

// 预览图片
const previewImage = () => {
  const fileUrl = getFileFullUrl(props.file.filePath);
  previewTitle.value = getFileName();
  previewUrl.value = fileUrl;
  currentPreviewUrl.value = fileUrl;
  previewDialogVisible.value = true;
};

// 在线预览Office和PDF文件
const previewOfficeFile = () => {
  // 如果是模板类型且需要请求
  if (props.templateType && props.templateApi) {
    loadAndPreviewTemplate();
    return;
  }

  const fileUrl = getFileFullUrl(props.file.filePath);
  previewTitle.value = getFileName();
  previewUrl.value = fileUrl;
  currentPreviewUrl.value = fileUrl;
  previewDialogVisible.value = true;
};

// 关闭预览
const closePreview = async () => {
  if (isUpdating.value) return;

  isUpdating.value = true;

  try {
    // 如果是全屏状态，先退出全屏
    if (isFullscreen.value) {
      isFullscreen.value = false;
      await nextTick();
    }

    // 重置状态
    previewDialogVisible.value = false;

    // 延迟清理URL，避免闪动
    setTimeout(() => {
      currentPreviewUrl.value = '';
    }, 200);

    // 通知父组件关闭
    emit('update:visible', false);
    emit('close');
  } finally {
    // 延迟重置更新标志
    setTimeout(() => {
      isUpdating.value = false;
    }, 300);
  }
};

// 切换全屏
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
};

// 下载文件
const downloadFile = async () => {
  // 如果是模板类型且需要请求
  if (props.templateType && props.templateApi) {
    await downloadTemplate();
    return;
  }

  const fileUrl = getFileFullUrl(props.file.filePath);
  const link = document.createElement('a');
  link.href = fileUrl;
  link.download = getFileName();
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 下载模板
const downloadTemplate = async () => {
  if (!props.templateType || !props.templateApi) return;

  isLoading.value = true;
  try {
    const res = await props.templateApi(props.templateType);
    if (res.code === 200 && res.data) {
      // 构建完整的URL
      const fileUrl = props.urlPrefix + res.data;
      const fileName = getTemplateFileName(props.templateType) + '.pdf';

      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      emit('load-success', res.data);
    } else {
      ElMessage.error(res.msg || '获取模板文件失败');
      emit('load-error', res.msg || '获取模板文件失败');
    }
  } catch (error) {
    console.error('下载模板失败', error);
    ElMessage.error('下载模板文件失败');
    emit('load-error', '下载模板文件失败');
  } finally {
    isLoading.value = false;
  }
};

// 监听ESC键退出全屏或关闭预览
onMounted(() => {
  document.addEventListener('keydown', handleEscKey);

  // 如果是预览模式，并且需要自动加载模板，则直接加载
  if (props.visible && props.templateType && props.templateApi && props.autoLoad) {
    loadAndPreviewTemplate();
  }
  // 否则，如果是普通文件预览，就初始化
  else if (props.visible && props.file && props.file.filePath) {
    initPreview();
  }
});

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleEscKey);
});

const handleEscKey = (event) => {
  if (event.key === 'Escape' && shouldShowDialog.value) {
    if (isFullscreen.value) {
      toggleFullscreen();
      return;
    }
    closePreview();
  }
};

</script>

<style lang="scss">
.file-view-container {
  position: relative;
  width: 100%;
  max-width: 120px;
  margin: 0 auto;
}

.file-preview {
  position: relative;
  width: 100%;
  height: 100px;
  margin-top: 10px;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.file-preview:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.file-thumbnail {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-icon {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  font-weight: bold;
  font-size: 14px;
}

.excel-icon {
  background-color: #e9f7f0;
  color: #217346;
  border: 1px solid #b5e0ca;
}

.word-icon {
  background-color: #e9f0f7;
  color: #2b579a;
  border: 1px solid #b5c6e0;
}

.pdf-icon {
  background-color: #fef2f0;
  color: #d54c2a;
  border: 1px solid #f6bbaa;
}

.document-icon {
  background-color: #f0f0f0;
  color: #666;
  border: 1px solid #ddd;
}

.file-actions {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.file-name-container {
  margin-top: 8px;
  text-align: center;
  padding: 0 4px;
}

.file-name {
  display: inline-block;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
}

/* 弹窗过渡动画 */
.dialog-fade-enter-active,
.dialog-fade-leave-active {
  transition: all 0.3s ease;
}

.dialog-fade-enter-from,
.dialog-fade-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

.dialog-fade-enter-to,
.dialog-fade-leave-from {
  opacity: 1;
  transform: scale(1);
}

/* 自定义弹窗样式 */
.custom-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.custom-dialog-container {
  width: 1200px;
  max-width: 95%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center center;
  will-change: transform, opacity;
}

.custom-dialog-header {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e4e7ed;
  min-height: 24px;
}

.custom-dialog-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn {
  padding: 0;
  font-size: 16px;
  color: #606266;
}

.action-btn:hover {
  color: #409EFF;
}

.action-btn[disabled] {
  color: #C0C4CC;
  cursor: not-allowed;
}

.close-btn {
  padding: 0;
  font-size: 20px;
  margin-left: 8px;
}

.content-wrapper {
  position: relative;
  transition: height 0.3s ease;
}

.custom-dialog-body {
  flex: 1;
  height: 100%;
  width: 100%;
  padding: 0;
  overflow: auto;
  position: relative;
}

.exit-fullscreen-icon {
  width: 1em;
  height: 1em;
}

/* 图片预览样式 */
.image-preview {
  max-width: 100%;
  max-height: 100%;
  display: block;
  margin: 0 auto;
  object-fit: contain;
}

/* 模板加载状态样式 */
.loading-template {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  gap: 10px;
  transition: opacity 0.3s ease;
}

.loading-template .el-icon {
  font-size: 32px;
  animation: spin 1s linear infinite;
}

.no-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  gap: 10px;
  transition: opacity 0.3s ease;
}

.no-preview-icon {
  font-size: 48px;
  color: #ddd;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>