<template>
  <div class="login-container">
    <div class="login-content">
      <div class="login-card animate__animated animate__fadeInUp">
        <div class="login-header">
          <h2 class="title">{{ title }}</h2>
          <p class="subtitle">欢迎回来，请登录您的账号</p>
        </div>

        <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form" @submit.prevent>
          <el-form-item prop="username">
            <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off" placeholder="账号">
              <template #prefix><svg-icon icon-class="user" class="input-icon" /></template>
            </el-input>
          </el-form-item>

          <el-form-item prop="password">
            <el-input v-model="loginForm.password" :type="passwordVisible ? 'text' : 'password'" size="large"
              auto-complete="off" placeholder="密码" @keyup.enter="handleLogin">
              <template #prefix><svg-icon icon-class="password" class="input-icon" /></template>
              <template #suffix>
                <el-icon class="password-toggle" @click="passwordVisible = !passwordVisible">
                  <svg-icon :icon-class="passwordVisible ? 'eye-open' : 'eye'" />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="code" v-if="captchaEnabled">
            <div class="captcha-container">
              <el-input v-model="loginForm.code" size="large" auto-complete="off" placeholder="验证码"
                @keyup.enter="handleLogin">
                <template #prefix><svg-icon icon-class="validCode" class="input-icon" /></template>
              </el-input>
              <div class="captcha-image" @click="getCode" title="点击刷新验证码">
                <img :src="codeUrl" alt="验证码" class="login-code-img" />
              </div>
            </div>
          </el-form-item>

          <div class="form-options">
            <el-checkbox v-model="loginForm.rememberMe">记住密码</el-checkbox>
            <a href="javascript:;" class="forget-password">忘记密码?</a>
          </div>

          <div class="form-actions">
            <el-button :loading="loading" size="large" type="primary" class="login-button" @click.prevent="handleLogin">
              <span v-if="!loading">登 录</span>
              <span v-else>登录中...</span>
            </el-button>

            <div class="register-link" v-if="register">
              <span>还没有账号?</span>
              <router-link class="link-type" to="/register">立即注册</router-link>
            </div>
          </div>
        </el-form>

        <div class="login-divider">
          <span>其他登录方式</span>
        </div>

        <div class="social-login">
          <div class="social-icon" title="微信登录">
            <svg-icon icon-class="wechat" />
          </div>
          <div class="social-icon" title="QQ登录">
            <svg-icon icon-class="qq" />
          </div>
          <div class="social-icon" title="企业微信登录">
            <svg-icon icon-class="wechat-work" />
          </div>
        </div>
      </div>
    </div>

    <div class="el-login-footer">
      <span>Copyright © 2018-{{ new Date().getFullYear() }} sux.vip All Rights Reserved.</span>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, getCurrentInstance, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getCodeImg } from "@/api/login"
import Cookies from "js-cookie"
import { encrypt, decrypt } from "@/utils/jsencrypt"
import useUserStore from '@/store/modules/user'
import 'animate.css'

// Constants and store setup
const title = import.meta.env.VITE_APP_TITLE
const userStore = useUserStore()
const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

// Reactive state
const loginForm = ref({
  username: "",
  password: "",
  rememberMe: false,
  code: "",
  uuid: ""
})

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }]
}

const codeUrl = ref("")
const loading = ref(false)
const passwordVisible = ref(false)
const captchaEnabled = ref(true)
const register = ref(false)
const redirect = ref(undefined)

// Watch for route changes to get redirect parameter
watch(route, (newRoute) => {
  redirect.value = newRoute.query && newRoute.query.redirect
}, { immediate: true })

// Methods
function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (!valid) return

    loading.value = true

    // Handle remember me functionality
    if (loginForm.value.rememberMe) {
      Cookies.set("username", loginForm.value.username, { expires: 30 })
      Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 })
      Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 })
    } else {
      Cookies.remove("username")
      Cookies.remove("password")
      Cookies.remove("rememberMe")
    }

    // Login process
    userStore.login(loginForm.value)
      .then(() => {
        const query = route.query
        const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
          if (cur !== "redirect") {
            acc[cur] = query[cur]
          }
          return acc
        }, {})
        router.push({ path: redirect.value || "/", query: otherQueryParams })
      })
      .catch(() => {
        loading.value = false
        if (captchaEnabled.value) {
          getCode()
        }
      })
  })
}

function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img
      loginForm.value.uuid = res.uuid
    }
  })
}

function getCookie() {
  const username = Cookies.get("username")
  const password = Cookies.get("password")
  const rememberMe = Cookies.get("rememberMe")

  if (username || password) {
    loginForm.value = {
      username: username === undefined ? "" : username,
      password: password === undefined ? "" : decrypt(password),
      rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
      code: "",
      uuid: ""
    }
  }
}

// Initialize
onMounted(() => {
  getCode()
  getCookie()
})
</script>

<style lang='scss' scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-size: cover;
  background-position: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("../assets/images/login-background.png");
    background-size: cover;
    background-position: center;
    z-index: 0;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
  }
}

.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 550px;
  padding: 15px 20px;
}

.login-card {
  width: 100%;
  padding: 30px 50px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.92);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 35px;

  .title {
    font-size: 30px;
    font-weight: 700;
    color: #303133;
    margin-bottom: 10px;
    letter-spacing: 0.5px;
    background: linear-gradient(to right, #3a8ffe, #9658fe);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .subtitle {
    font-size: 16px;
    color: #606266;
    margin: 0;
    font-weight: 400;
  }
}

.login-form {
  .el-input {
    height: 52px;
    margin-bottom: 5px;

    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 1px #dcdfe6 inset;
      transition: all 0.2s;
      border-radius: 8px;

      &:hover {
        box-shadow: 0 0 0 1px #c0c4cc inset;
      }

      &.is-focus {
        box-shadow: 0 0 0 1px #409eff inset;
      }
    }

    input {
      height: 52px;
      font-size: 15px;
      padding-left: 5px;
    }
  }

  .input-icon {
    height: 20px;
    width: 20px;
    margin-right: 8px;
    color: #909399;
  }
}

.captcha-container {
  display: flex;
  gap: 12px;

  .el-input {
    flex: 1;
  }

  .captcha-image {
    cursor: pointer;
    border-radius: 8px;
    overflow: hidden;
    height: 52px;
    box-shadow: 0 0 0 1px #dcdfe6;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 0 0 1px #409eff;
    }

    img {
      height: 100%;
      vertical-align: middle;
    }
  }
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 5px 0 25px;

  .forget-password {
    color: #409EFF;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.2s ease;

    &:hover {
      color: #66b1ff;
      transform: translateX(3px);
    }
  }
}

.form-actions {
  .login-button {
    width: 100%;
    height: 52px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
    margin-bottom: 20px;
    letter-spacing: 1px;
    background: linear-gradient(to right, #3a8ffe, #9658fe);
    border: none;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(58, 143, 254, 0.35);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .register-link {
    display: flex;
    justify-content: center;
    gap: 6px;
    font-size: 14px;

    span {
      color: #606266;
    }

    .link-type {
      color: #409EFF;
      text-decoration: none;
      transition: all 0.2s ease;

      &:hover {
        color: #66b1ff;
        text-decoration: underline;
      }
    }
  }
}

.login-divider {
  display: flex;
  align-items: center;
  margin: 30px 0;
  color: #909399;
  font-size: 14px;

  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background: #e4e7ed;
  }

  span {
    padding: 0 15px;
  }
}

.social-login {
  display: flex;
  justify-content: center;
  gap: 20px;

  .social-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: #f5f7fa;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;

    svg {
      font-size: 22px;
      color: #606266;
    }

    &:hover {
      background: #409EFF;
      transform: translateY(-3px);

      svg {
        color: white;
      }
    }
  }
}

.password-toggle {
  cursor: pointer;
  font-size: 16px;
  color: #909399;
  transition: color 0.2s;

  &:hover {
    color: #606266;
  }
}

.el-login-footer {
  position: absolute;
  bottom: 20px;
  width: 100%;
  text-align: center;
  color: #fff;
  font-size: 13px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  z-index: 2;
}

/* Responsive styles */
@media (max-width: 768px) {
  .login-card {
    padding: 30px 25px;
  }

  .login-header .title {
    font-size: 26px;
  }

  .social-login {
    gap: 15px;
  }
}

@media (max-height: 700px) {
  .login-container {
    align-items: flex-start;
    padding: 40px 0;
  }

  .el-login-footer {
    position: relative;
    margin-top: 30px;
  }
}
</style>
