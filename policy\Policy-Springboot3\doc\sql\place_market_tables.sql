-- 场地展示、零工市场基础信息维护、用工信息维护相关表结构
-- 创建时间：2025-07-23

-- 场地信息表
DROP TABLE IF EXISTS `place_info`;
CREATE TABLE `place_info` (
  `place_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '场地ID',
  `place_name` varchar(200) NOT NULL COMMENT '场地名称',
  `place_code` varchar(50) COMMENT '场地编码',
  `place_type` varchar(50) NOT NULL COMMENT '场地类型（创业园区/孵化器/众创空间/产业园等）',
  `place_level` varchar(50) COMMENT '场地等级（国家级/省级/市级/区级）',
  `place_area` decimal(10,2) COMMENT '场地面积（平方米）',
  `usable_area` decimal(10,2) COMMENT '可使用面积（平方米）',
  `address` varchar(500) NOT NULL COMMENT '详细地址',
  `region_code` varchar(20) COMMENT '区域代码',
  `region_name` varchar(100) COMMENT '区域名称',
  `longitude` decimal(10,6) COMMENT '经度',
  `latitude` decimal(10,6) COMMENT '纬度',
  `contact_person` varchar(100) COMMENT '联系人',
  `contact_phone` varchar(20) COMMENT '联系电话',
  `contact_email` varchar(100) COMMENT '联系邮箱',
  `company_count` int(11) DEFAULT 0 COMMENT '已入驻企业数量',
  `available_positions` int(11) DEFAULT 0 COMMENT '可提供工位数',
  `occupied_positions` int(11) DEFAULT 0 COMMENT '已占用工位数',
  `rent_price_min` decimal(10,2) COMMENT '最低租金（元/月/平方米）',
  `rent_price_max` decimal(10,2) COMMENT '最高租金（元/月/平方米）',
  `operation_mode` varchar(50) COMMENT '运营模式（自营/委托运营/合作运营）',
  `industry_direction` varchar(200) COMMENT '行业方向（多个用逗号分隔）',
  `service_facilities` text COMMENT '服务设施（JSON格式存储）',
  `preferential_policies` text COMMENT '优惠政策描述',
  `apply_start_date` date COMMENT '招商开始时间',
  `apply_end_date` date COMMENT '招商结束时间',
  `apply_time_status` tinyint(1) DEFAULT 0 COMMENT '招商时间状态（0长期 1定期）',
  `is_open_settle` tinyint(1) DEFAULT 1 COMMENT '是否开放入驻（0否 1是）',
  `image_url` varchar(500) COMMENT '场地主图片URL',
  `image_gallery` text COMMENT '场地图片集（JSON格式存储多张图片URL）',
  `description` text COMMENT '场地详细描述',
  `notice_detail` text COMMENT '场地公告详情',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `is_featured` tinyint(1) DEFAULT 0 COMMENT '是否推荐（0否 1是）',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序号',
  `view_count` int(11) DEFAULT 0 COMMENT '浏览次数',
  `create_id` bigint(20) COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` bigint(20) COMMENT '更新者ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) COMMENT '备注',
  PRIMARY KEY (`place_id`),
  KEY `idx_place_type` (`place_type`),
  KEY `idx_place_level` (`place_level`),
  KEY `idx_region_code` (`region_code`),
  KEY `idx_status` (`status`),
  KEY `idx_is_featured` (`is_featured`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='场地信息表';

-- 零工市场基础信息表
DROP TABLE IF EXISTS `labor_market_info`;
CREATE TABLE `labor_market_info` (
  `market_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '市场ID',
  `market_name` varchar(200) NOT NULL COMMENT '市场名称',
  `market_code` varchar(50) COMMENT '市场编码',
  `market_type` varchar(50) NOT NULL COMMENT '市场类型（综合市场/专业市场/临时市场）',
  `address` varchar(500) NOT NULL COMMENT '市场地址',
  `region_code` varchar(20) COMMENT '区域代码',
  `region_name` varchar(100) COMMENT '区域名称',
  `longitude` decimal(10,6) COMMENT '经度',
  `latitude` decimal(10,6) COMMENT '纬度',
  `contact_person` varchar(100) COMMENT '联系人',
  `contact_phone` varchar(20) COMMENT '联系电话',
  `contact_email` varchar(100) COMMENT '联系邮箱',
  `operating_hours` varchar(200) COMMENT '营业时间',
  `service_categories` text COMMENT '服务类别（JSON格式存储）',
  `worker_capacity` int(11) DEFAULT 0 COMMENT '零工容纳量',
  `current_worker_count` int(11) DEFAULT 0 COMMENT '当前零工数量',
  `daily_avg_demand` int(11) DEFAULT 0 COMMENT '日均用工需求',
  `peak_demand_time` varchar(100) COMMENT '用工高峰时段',
  `management_fee` decimal(10,2) COMMENT '管理费用（元/人/天）',
  `service_fee_rate` decimal(5,2) COMMENT '服务费率（%）',
  `facilities` text COMMENT '配套设施（JSON格式存储）',
  `safety_measures` text COMMENT '安全措施描述',
  `image_url` varchar(500) COMMENT '市场主图片URL',
  `image_gallery` text COMMENT '市场图片集（JSON格式存储多张图片URL）',
  `description` text COMMENT '市场详细描述',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `is_featured` tinyint(1) DEFAULT 0 COMMENT '是否推荐（0否 1是）',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序号',
  `view_count` int(11) DEFAULT 0 COMMENT '浏览次数',
  `create_id` bigint(20) COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` bigint(20) COMMENT '更新者ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) COMMENT '备注',
  PRIMARY KEY (`market_id`),
  KEY `idx_market_type` (`market_type`),
  KEY `idx_region_code` (`region_code`),
  KEY `idx_status` (`status`),
  KEY `idx_is_featured` (`is_featured`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='零工市场基础信息表';

-- 用工信息表（扩展现有的job_posting表功能）
DROP TABLE IF EXISTS `employment_info`;
CREATE TABLE `employment_info` (
  `employment_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用工信息ID',
  `title` varchar(200) NOT NULL COMMENT '用工标题',
  `employment_type` varchar(50) NOT NULL COMMENT '用工类型（日结/周结/月结/计件）',
  `work_category` varchar(100) NOT NULL COMMENT '工作类别（服务员/保洁/搬运工/销售/厨师助手/快递员/保安等）',
  `work_location` varchar(200) COMMENT '工作地点',
  `region_code` varchar(20) COMMENT '区域代码',
  `region_name` varchar(100) COMMENT '区域名称',
  `salary_type` varchar(20) NOT NULL COMMENT '薪资类型（hourly/daily/monthly/piece）',
  `salary_min` decimal(10,2) COMMENT '最低薪资',
  `salary_max` decimal(10,2) COMMENT '最高薪资',
  `work_hours_per_day` int(11) COMMENT '每日工作小时数',
  `work_days_per_week` int(11) COMMENT '每周工作天数',
  `start_date` date COMMENT '开始日期',
  `end_date` date COMMENT '结束日期',
  `positions_needed` int(11) DEFAULT 1 COMMENT '需要人数',
  `positions_filled` int(11) DEFAULT 0 COMMENT '已招聘人数',
  `education_required` varchar(50) COMMENT '学历要求',
  `experience_required` varchar(100) COMMENT '经验要求',
  `age_min` int(11) COMMENT '最小年龄要求',
  `age_max` int(11) COMMENT '最大年龄要求',
  `gender_required` varchar(10) COMMENT '性别要求（male/female/unlimited）',
  `skills_required` text COMMENT '技能要求（JSON格式存储）',
  `work_description` text COMMENT '工作描述',
  `welfare_benefits` text COMMENT '福利待遇',
  `contact_person` varchar(100) COMMENT '联系人',
  `contact_phone` varchar(20) COMMENT '联系电话',
  `contact_email` varchar(100) COMMENT '联系邮箱',
  `company_name` varchar(200) COMMENT '公司名称',
  `company_address` varchar(500) COMMENT '公司地址',
  `company_description` text COMMENT '公司描述',
  `urgency_level` varchar(20) DEFAULT 'normal' COMMENT '紧急程度（urgent/high/normal/low）',
  `application_deadline` datetime COMMENT '申请截止时间',
  `status` varchar(20) DEFAULT 'draft' COMMENT '状态（draft/published/paused/closed/completed）',
  `is_verified` tinyint(1) DEFAULT 0 COMMENT '是否已验证（0否 1是）',
  `is_featured` tinyint(1) DEFAULT 0 COMMENT '是否推荐（0否 1是）',
  `view_count` int(11) DEFAULT 0 COMMENT '浏览次数',
  `application_count` int(11) DEFAULT 0 COMMENT '申请次数',
  `publisher_user_id` bigint(20) NOT NULL COMMENT '发布者用户ID',
  `create_id` bigint(20) COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` bigint(20) COMMENT '更新者ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) COMMENT '备注',
  PRIMARY KEY (`employment_id`),
  KEY `idx_employment_type` (`employment_type`),
  KEY `idx_work_category` (`work_category`),
  KEY `idx_salary_type` (`salary_type`),
  KEY `idx_region_code` (`region_code`),
  KEY `idx_status` (`status`),
  KEY `idx_is_featured` (`is_featured`),
  KEY `idx_publisher_user_id` (`publisher_user_id`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用工信息表';

-- 插入示例数据

-- 场地信息示例数据
INSERT INTO `place_info` (
  `place_name`, `place_code`, `place_type`, `place_level`, `place_area`, `usable_area`,
  `address`, `region_code`, `region_name`, `contact_person`, `contact_phone`, `contact_email`,
  `company_count`, `available_positions`, `occupied_positions`, `rent_price_min`, `rent_price_max`,
  `operation_mode`, `industry_direction`, `service_facilities`, `preferential_policies`,
  `apply_start_date`, `apply_end_date`, `apply_time_status`, `is_open_settle`,
  `image_url`, `description`, `notice_detail`, `status`, `is_featured`, `sort_order`, `create_id`
) VALUES
(
  '青岛国际创新园', 'QDICX001', '创业园区', '国家级', 50000.00, 35000.00,
  '青岛市市南区香港中路100号', '370202', '市南区', '张经理', '0532-88888888', '<EMAIL>',
  85, 200, 170, 80.00, 120.00, '自营', '互联网,人工智能,生物医药',
  '["会议室", "路演厅", "咖啡厅", "健身房", "停车场", "24小时安保"]',
  '前三年租金减免50%，提供创业导师服务，优先推荐投资机构',
  '2025-01-01', '2025-12-31', 0, 1,
  './image/place1.jpg', '青岛市重点打造的国际化创新创业园区，配套设施完善，政策支持力度大',
  '欢迎各类创新创业企业入驻，共同打造创新生态圈', '0', 1, 1, 1
),
(
  '崂山科技孵化器', 'LSKJFH002', '孵化器', '省级', 30000.00, 22000.00,
  '青岛市崂山区科技路200号', '370212', '崂山区', '李主任', '0532-77777777', '<EMAIL>',
  45, 120, 95, 60.00, 90.00, '委托运营', '软件开发,电子商务,新材料',
  '["创客空间", "实验室", "产品展示厅", "商务中心", "餐厅"]',
  '提供免费办公场地6个月，专业孵化服务，投融资对接',
  '2025-03-01', '2025-11-30', 1, 1,
  './image/place2.jpg', '专注于科技型企业孵化，拥有完善的孵化服务体系',
  '现面向全市招募优秀科技创业项目', '0', 1, 2, 1
),
(
  '市北众创空间', 'SBZCKG003', '众创空间', '市级', 15000.00, 12000.00,
  '青岛市市北区重庆南路150号', '370203', '市北区', '王总', '0532-66666666', '<EMAIL>',
  28, 80, 65, 40.00, 70.00, '合作运营', '文化创意,设计服务,教育培训',
  '["开放办公区", "独立工位", "会议室", "打印复印", "茶水间"]',
  '工位租金优惠，提供创业培训，定期举办创业沙龙',
  '2025-02-01', '2025-10-31', 1, 1,
  './image/place3.jpg', '面向初创企业和个人创业者的低成本创业空间',
  '为创业者提供便利的办公环境和创业服务', '0', 0, 3, 1
);

-- 零工市场基础信息示例数据
INSERT INTO `labor_market_info` (
  `market_name`, `market_code`, `market_type`, `address`, `region_code`, `region_name`,
  `contact_person`, `contact_phone`, `contact_email`, `operating_hours`, `service_categories`,
  `worker_capacity`, `current_worker_count`, `daily_avg_demand`, `peak_demand_time`,
  `management_fee`, `service_fee_rate`, `facilities`, `safety_measures`,
  `image_url`, `description`, `status`, `is_featured`, `sort_order`, `create_id`
) VALUES
(
  '青岛市中心零工市场', 'QDZXLG001', '综合市场', '青岛市市南区中山路88号', '370202', '市南区',
  '赵经理', '0532-55555555', '<EMAIL>', '06:00-20:00',
  '["服务员", "保洁", "搬运工", "销售", "厨师助手", "快递员", "保安", "临时工"]',
  500, 320, 180, '08:00-10:00,14:00-16:00', 10.00, 5.00,
  '["休息区", "饮水设施", "卫生间", "信息发布栏", "监控系统", "医务室"]',
  '实名制管理，24小时监控，定期安全培训，购买意外保险',
  './image/market1.jpg', '青岛市最大的综合性零工市场，服务类别齐全，管理规范',
  '0', 1, 1, 1
),
(
  '崂山区专业零工市场', 'LSZYLG002', '专业市场', '青岛市崂山区海尔路300号', '370212', '崂山区',
  '孙主管', '0532-44444444', '<EMAIL>', '07:00-19:00',
  '["技术工人", "装修工", "电工", "水暖工", "园艺工", "维修工"]',
  200, 150, 80, '09:00-11:00,15:00-17:00', 15.00, 8.00,
  '["技能培训室", "工具存放区", "安全防护用品", "休息室"]',
  '持证上岗，技能认证，安全操作培训，工伤保险覆盖',
  './image/market2.jpg', '专注于技术型零工服务，工人技能水平较高',
  '0', 1, 2, 1
),
(
  '市北区临时零工市场', 'SBLSLG003', '临时市场', '青岛市市北区台东路120号', '370203', '市北区',
  '钱站长', '0532-33333333', '<EMAIL>', '05:30-18:00',
  '["日结工", "小时工", "临时搬运", "活动服务", "清洁工"]',
  300, 200, 120, '06:00-08:00,17:00-18:00', 8.00, 3.00,
  '["候工区", "信息公告栏", "简易休息设施", "饮水点"]',
  '身份验证，现场管理，基础安全提醒',
  './image/market3.jpg', '主要服务临时性用工需求，灵活便民',
  '0', 0, 3, 1
);

-- 用工信息示例数据
INSERT INTO `employment_info` (
  `title`, `employment_type`, `work_category`, `work_location`, `region_code`, `region_name`,
  `salary_type`, `salary_min`, `salary_max`, `work_hours_per_day`, `work_days_per_week`,
  `start_date`, `end_date`, `positions_needed`, `education_required`, `experience_required`,
  `age_min`, `age_max`, `gender_required`, `skills_required`, `work_description`, `welfare_benefits`,
  `contact_person`, `contact_phone`, `contact_email`, `company_name`, `company_address`,
  `company_description`, `urgency_level`, `application_deadline`, `status`, `is_verified`,
  `is_featured`, `publisher_user_id`, `create_id`
) VALUES
(
  '餐厅服务员招聘', '日结', '服务员', '青岛市市南区香港中路', '370202', '市南区',
  'daily', 120.00, 150.00, 8, 6, '2025-07-25', '2025-12-31', 5,
  '高中', '有餐厅服务经验优先', 18, 45, 'unlimited',
  '["餐厅服务", "客户接待", "收银操作", "礼仪服务"]',
  '负责餐厅日常服务工作，包括点餐、上菜、收银等，要求服务态度好，沟通能力强',
  '包工作餐，提供制服，表现优秀者可转正',
  '张经理', '13800138001', '<EMAIL>', '海景餐厅',
  '青岛市市南区香港中路88号', '知名海鲜餐厅，环境优雅，客流量大',
  'normal', '2025-08-15 18:00:00', 'published', 1, 1, 1001, 1
),
(
  '保洁员长期招聘', '月结', '保洁', '青岛市崂山区科技路', '370212', '崂山区',
  'monthly', 3500.00, 4200.00, 8, 6, '2025-08-01', '2025-12-31', 3,
  '不限', '有保洁工作经验', 25, 55, 'female',
  '["清洁技能", "使用清洁设备", "垃圾分类"]',
  '负责办公楼保洁工作，包括地面清洁、垃圾清理、卫生间清洁等',
  '五险一金，带薪年假，节日福利',
  '李主管', '13900139001', '<EMAIL>', '青岛清洁服务公司',
  '青岛市崂山区科技路200号', '专业清洁服务公司，服务品质优良',
  'normal', '2025-08-20 17:00:00', 'published', 1, 0, 1002, 1
),
(
  '搬运工临时招聘', '日结', '搬运工', '青岛市市北区重庆南路', '370203', '市北区',
  'daily', 200.00, 250.00, 10, 7, '2025-07-24', '2025-08-10', 10,
  '不限', '身体健康，能吃苦耐劳', 20, 50, 'male',
  '["体力劳动", "货物搬运", "团队协作"]',
  '负责货物搬运工作，要求身体健康，能够承受重体力劳动',
  '按日结算，多劳多得，提供工作餐',
  '王总', '13700137001', '<EMAIL>', '青岛物流公司',
  '青岛市市北区重庆南路150号', '大型物流企业，业务量稳定',
  'urgent', '2025-07-25 12:00:00', 'published', 1, 1, 1003, 1
),
(
  '销售助理兼职', '周结', '销售', '青岛市市南区中山路', '370202', '市南区',
  'weekly', 800.00, 1200.00, 6, 5, '2025-08-01', '2025-11-30', 2,
  '大专', '有销售经验或相关专业', 22, 35, 'unlimited',
  '["销售技巧", "客户沟通", "产品介绍", "办公软件"]',
  '协助销售经理开展销售工作，包括客户接待、产品介绍、订单处理等',
  '提成奖励，培训机会，弹性工作时间',
  '陈经理', '13600136001', '<EMAIL>', '青岛贸易公司',
  '青岛市市南区中山路66号', '专业贸易公司，产品质量优良',
  'normal', '2025-08-10 18:00:00', 'published', 1, 0, 1004, 1
),
(
  '厨师助手急招', '日结', '厨师助手', '青岛市崂山区海尔路', '370212', '崂山区',
  'daily', 150.00, 180.00, 10, 6, '2025-07-24', '2025-09-30', 3,
  '中专', '有厨房工作经验', 20, 40, 'male',
  '["食材准备", "菜品制作", "厨房清洁", "中式烹饪"]',
  '协助主厨进行食材准备、菜品制作等工作，要求刀工娴熟，工作效率高',
  '包食宿，提供工作服，技能培训',
  '马师傅', '13500135001', '<EMAIL>', '海鲜大酒店',
  '青岛市崂山区海尔路300号', '五星级酒店，厨房设备先进',
  'high', '2025-07-26 20:00:00', 'published', 1, 1, 1005, 1
);
