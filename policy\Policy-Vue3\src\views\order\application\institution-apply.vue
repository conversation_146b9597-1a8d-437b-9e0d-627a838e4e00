<template>
  <div class="institution-application-container standalone-page">
    <!-- 关闭按钮 -->
    <div class="close-button" @click="closePage">
      <el-icon size="24">
        <Close />
      </el-icon>
    </div>

    <!-- 页面标题 -->
    <div class="page-header">
      <h1>培训机构申请平台</h1>
      <p>欢迎使用培训机构申请平台，您可以在这里申请承接各类培训项目</p>
    </div>

    <!-- 培训项目列表 -->
    <div class="training-list">
      <div class="list-header">
        <h2>可申请培训项目</h2>
        <div class="list-stats" v-if="trainingList.length > 0">
          <span class="stats-item">
            <el-icon>
              <Document />
            </el-icon>
            共 {{ trainingList.length }} 个培训项目
          </span>
        </div>
      </div>

      <div class="training-items" v-loading="loading">
        <!-- 空状态 -->
        <div v-if="!loading && trainingList.length === 0" class="empty-state">
          <div class="empty-content">
            <div class="empty-icon">
              <el-icon size="80">
                <Document />
              </el-icon>
            </div>
            <h3>暂无培训项目</h3>
            <p>当前没有可申请的培训项目，请稍后再来查看</p>
          </div>
        </div>

        <div v-for="training in trainingList" :key="training.orderId" class="training-item" :class="{
          'has-application': training.institutionApplication,
          'application-approved': training.applicationStatus === '1',
          'application-rejected': training.applicationStatus === '2',
          'application-pending': training.applicationStatus === '0',
          'application-cancelled': training.applicationStatus === '3'
        }">
          <div class="training-content">
            <div class="training-header">
              <h3 class="training-title">{{ training.orderTitle }}</h3>
              <el-tag :type="getTypeTagType(training.trainingType)" size="small">
                {{ training.trainingType }}
              </el-tag>
            </div>

            <p class="training-description">{{ training.orderDescription }}</p>

            <div class="training-info">
              <div class="info-row">
                <div class="info-item">
                  <span class="label">培训级别：</span>
                  <span class="value">{{ training.trainingLevel }}</span>
                </div>
                <div class="info-item">
                  <span class="label">培训时长：</span>
                  <span class="value">{{ training.trainingDuration }}小时</span>
                </div>
                <div class="info-item">
                  <span class="label">培训费用：</span>
                  <span class="value price">{{ training.trainingFee ? '￥' + training.trainingFee : '面议' }}</span>
                </div>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <span class="label">培训时间：</span>
                  <span class="value">{{ formatDate(training.startDate) }} 至 {{ formatDate(training.endDate) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">培训地址：</span>
                  <span class="value">{{ training.trainingAddress }}</span>
                </div>
                <div class="info-item">
                  <span class="label">申请截止：</span>
                  <span class="value">{{ formatDate(training.registrationDeadline) }}</span>
                </div>
              </div>

              <div class="info-row" v-if="training.institutionApplication">
                <div class="info-item">
                  <span class="label">申请状态：</span>
                  <el-tag :type="getApplicationStatusTagType(training.applicationStatus)" size="small">
                    {{ getApplicationStatusText(training.applicationStatus) }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <span class="label">申请时间：</span>
                  <span class="value">{{ formatDate(training.institutionApplication.applicationTime) }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="training-actions">
            <!-- 未申请过或申请被拒绝，可以申请 -->
            <el-button v-if="training.canApply" type="primary" size="default" @click="handleApply(training)"
              :disabled="!canApplyTraining(training)" class="action-btn primary-btn">
              <el-icon>
                <Plus />
              </el-icon>
              {{ training.institutionApplication ? '重新申请' : '立即申请' }}
            </el-button>

            <!-- 已申请，显示申请状态 -->
            <el-button v-else-if="training.institutionApplication"
              :type="getApplicationButtonType(training.applicationStatus)" size="default" disabled class="status-btn">
              <el-icon>
                <InfoFilled />
              </el-icon>
              {{ getApplicationStatusText(training.applicationStatus) }}
            </el-button>

            <!-- 查看申请信息 -->
            <el-button v-if="training.institutionApplication" type="success" size="default"
              @click="handleViewApplication(training.institutionApplication)" class="action-btn view-btn">
              <el-icon>
                <View />
              </el-icon>
              查看申请
            </el-button>

            <!-- 取消申请 -->
            <el-button v-if="training.institutionApplication && training.applicationStatus === '0'" type="warning"
              size="default" @click="handleCancelApplication(training.institutionApplication)"
              class="action-btn cancel-btn">
              <el-icon>
                <Close />
              </el-icon>
              取消申请
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 申请弹窗 -->
    <el-dialog v-model="applyDialogVisible" :title="`机构申请 - ${currentTraining?.orderTitle}`" width="1000px"
      :close-on-click-modal="false" append-to-body>
      <div class="apply-form">
        <el-form ref="applyFormRef" :model="applyForm" :rules="applyRules" label-width="120px">
          <!-- 基本信息 -->
          <div class="form-section">
            <h4 class="section-title">机构基本信息</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="机构名称" prop="institutionName">
                  <el-input v-model="applyForm.institutionName" placeholder="请输入机构名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="机构代码" prop="institutionCode">
                  <el-input v-model="applyForm.institutionCode" placeholder="统一社会信用代码" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="法定代表人" prop="legalPerson">
                  <el-input v-model="applyForm.legalPerson" placeholder="请输入法定代表人" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="机构类型" prop="institutionType">
                  <el-select v-model="applyForm.institutionType" placeholder="请选择机构类型" style="width: 100%">
                    <el-option label="企业" value="企业"></el-option>
                    <el-option label="事业单位" value="事业单位"></el-option>
                    <el-option label="社会组织" value="社会组织"></el-option>
                    <el-option label="其他" value="其他"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="成立时间" prop="establishedDate">
                  <el-date-picker v-model="applyForm.establishedDate" type="date" placeholder="选择成立时间"
                    style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册资本" prop="registeredCapital">
                  <el-input-number v-model="applyForm.registeredCapital" :min="0" :precision="2" placeholder="万元"
                    style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="机构地址" prop="institutionAddress">
              <el-input v-model="applyForm.institutionAddress" placeholder="请输入机构详细地址" />
            </el-form-item>

            <el-form-item label="经营范围" prop="businessScope">
              <el-input v-model="applyForm.businessScope" type="textarea" :rows="3" placeholder="请输入经营范围" />
            </el-form-item>
          </div>

          <!-- 联系信息 -->
          <div class="form-section">
            <h4 class="section-title">联系信息</h4>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="联系人" prop="contactPerson">
                  <el-input v-model="applyForm.contactPerson" placeholder="请输入联系人" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系电话" prop="contactPhone">
                  <el-input v-model="applyForm.contactPhone" placeholder="请输入联系电话" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系邮箱" prop="contactEmail">
                  <el-input v-model="applyForm.contactEmail" placeholder="请输入联系邮箱" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 培训能力 -->
          <div class="form-section">
            <h4 class="section-title">培训能力</h4>
            <el-form-item label="培训经验" prop="trainingExperience">
              <el-input v-model="applyForm.trainingExperience" type="textarea" :rows="4"
                placeholder="请详细描述您的培训经验，包括培训年限、培训领域、培训规模等" />
            </el-form-item>

            <el-form-item label="培训能力" prop="trainingCapacity">
              <el-input v-model="applyForm.trainingCapacity" type="textarea" :rows="4"
                placeholder="请详细描述您的培训能力，包括培训体系、培训方法、培训效果等" />
            </el-form-item>

            <el-form-item label="培训计划" prop="trainingPlan">
              <el-input v-model="applyForm.trainingPlan" type="textarea" :rows="4" placeholder="请详细描述针对此培训项目的具体培训计划" />
            </el-form-item>

            <el-form-item label="师资信息" prop="teacherInfo">
              <el-input v-model="applyForm.teacherInfo" type="textarea" :rows="4"
                placeholder="请详细描述师资队伍情况，包括讲师数量、资质、经验等" />
            </el-form-item>

            <el-form-item label="设施设备" prop="facilityInfo">
              <el-input v-model="applyForm.facilityInfo" type="textarea" :rows="3" placeholder="请描述培训场地、设备等硬件设施情况" />
            </el-form-item>
          </div>

          <!-- 材料上传 -->
          <div class="form-section">
            <h4 class="section-title">申请材料上传</h4>

            <div class="required-materials">
              <div class="material-item" v-for="(material, index) in requiredMaterials" :key="index"
                   :class="{ 'required-material': material.required, 'optional-material': !material.required }">
                <div class="material-header">
                  <div class="material-info">
                    <div class="material-name">
                      <el-icon class="material-icon">
                        <Document />
                      </el-icon>
                      <span>{{ material.name }}</span>
                    </div>
                    <div class="material-status">
                      <el-tag v-if="material.required" type="danger" size="small">必需</el-tag>
                      <el-tag v-else type="success" size="small">可选</el-tag>
                    </div>
                  </div>
                </div>

                <div class="material-upload">
                  <FileUpload v-model:value="material.files" :limit="5" :file-size="10"
                    :file-type="['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png']" :is-show-tip="true"
                    @fileLoad="(data) => handleFileLoad(data, index)" />
                </div>
              </div>
            </div>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="applyDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmitApply">
            提交申请
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 申请详情弹窗 -->
    <el-dialog v-model="viewDialogVisible" title="申请详情" width="1000px" append-to-body destroy-on-close>
      <div v-if="currentApplication" class="application-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h4 class="section-title">基本信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="机构名称">{{ currentApplication.institutionName }}</el-descriptions-item>
            <el-descriptions-item label="机构代码">{{ currentApplication.institutionCode }}</el-descriptions-item>
            <el-descriptions-item label="法定代表人">{{ currentApplication.legalPerson }}</el-descriptions-item>
            <el-descriptions-item label="机构类型">{{ currentApplication.institutionType }}</el-descriptions-item>
            <el-descriptions-item label="联系人">{{ currentApplication.contactPerson }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ currentApplication.contactPhone }}</el-descriptions-item>
            <el-descriptions-item label="联系邮箱">{{ currentApplication.contactEmail }}</el-descriptions-item>
            <el-descriptions-item label="成立时间">{{ formatDate(currentApplication.establishedDate)
              }}</el-descriptions-item>
            <el-descriptions-item label="注册资本">{{ currentApplication.registeredCapital }}万元</el-descriptions-item>
            <el-descriptions-item label="申请状态">
              <el-tag :type="getApplicationStatusTagType(currentApplication.applicationStatus)">
                {{ getApplicationStatusText(currentApplication.applicationStatus) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="申请时间">{{ formatDate(currentApplication.applicationTime)
              }}</el-descriptions-item>
            <el-descriptions-item label="机构地址" :span="2">{{ currentApplication.institutionAddress
              }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 培训能力信息 -->
        <div class="detail-section">
          <h4 class="section-title">培训能力</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="经营范围">{{ currentApplication.businessScope || '--' }}</el-descriptions-item>
            <el-descriptions-item label="培训经验">{{ currentApplication.trainingExperience || '--'
              }}</el-descriptions-item>
            <el-descriptions-item label="培训能力">{{ currentApplication.trainingCapacity || '--' }}</el-descriptions-item>
            <el-descriptions-item label="培训计划">{{ currentApplication.trainingPlan || '--' }}</el-descriptions-item>
            <el-descriptions-item label="师资信息">{{ currentApplication.teacherInfo || '--' }}</el-descriptions-item>
            <el-descriptions-item label="设施设备">{{ currentApplication.facilityInfo || '--' }}</el-descriptions-item>
            <el-descriptions-item v-if="currentApplication.applicationNote" label="申请备注">{{
              currentApplication.applicationNote }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 申请材料 -->
        <div class="detail-section">
          <h4 class="section-title">申请材料</h4>
          <div class="uploaded-materials">
            <div class="material-item" v-for="(material, index) in viewMaterials" :key="index">
              <div class="material-header">
                <div class="material-info">
                  <div class="material-name">
                    <el-icon class="material-icon">
                      <Document />
                    </el-icon>
                    <span>{{ material.name }}</span>
                  </div>
                  <div class="material-status">
                    <el-tag v-if="material.files && material.files.length > 0" type="success" size="small">
                      已上传 {{ material.files.length }} 个文件
                    </el-tag>
                    <el-tag v-else type="info" size="small">未上传</el-tag>
                  </div>
                </div>
              </div>

              <div class="material-files" v-if="material.files && material.files.length > 0">
                <div class="file-grid">
                  <div class="file-card" v-for="(file, fileIndex) in material.files" :key="fileIndex">
                    <FileView
                      :file="{ filePath: file.url || file.filePath, sourceFileName: file.name || file.fileName }" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 审核信息 -->
        <div class="detail-section" v-if="currentApplication.applicationStatus !== '0'">
          <h4 class="section-title">审核信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="审核时间">{{ formatDate(currentApplication.reviewTime) }}</el-descriptions-item>
            <el-descriptions-item label="审核人">{{ currentApplication.reviewer || '--' }}</el-descriptions-item>
            <el-descriptions-item v-if="currentApplication.reviewComment" label="审核意见" :span="2">{{
              currentApplication.reviewComment }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, InfoFilled, View, Close, Document } from '@element-plus/icons-vue'
import { listTrainingOrder } from '@/api/training/order'
import {
  submitTrainingInstitutionApplication,
  updateTrainingInstitutionApplicationPublic,
  getMyInstitutionApplications,
  cancelMyInstitutionApplication
} from '@/api/training/institutionApplication'
import FileUpload from '@/components/FileUpload/index.vue'
import FileView from '@/components/FileView/index.vue'

// 响应式数据
const loading = ref(false)
const trainingList = ref([])
const applyDialogVisible = ref(false)
const viewDialogVisible = ref(false)
const submitLoading = ref(false)
const currentTraining = ref(null)
const currentApplication = ref(null)
const applyFormRef = ref(null)

// 申请表单数据
const applyForm = reactive({
  orderId: null,
  institutionName: '',
  institutionCode: '',
  legalPerson: '',
  contactPerson: '',
  contactPhone: '',
  contactEmail: '',
  institutionAddress: '',
  institutionType: '',
  establishedDate: null,
  registeredCapital: null,
  businessScope: '',
  trainingExperience: '',
  trainingCapacity: '',
  trainingPlan: '',
  teacherInfo: '',
  facilityInfo: '',
  applicationNote: ''
})

// 申请表单验证规则
const applyRules = {
  institutionName: [
    { required: true, message: '请输入机构名称', trigger: 'blur' },
    { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  legalPerson: [
    { required: true, message: '请输入法定代表人', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  contactPerson: [
    { required: true, message: '请输入联系人', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  contactEmail: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  institutionAddress: [
    { required: true, message: '请输入机构地址', trigger: 'blur' },
    { min: 5, max: 500, message: '长度在 5 到 500 个字符', trigger: 'blur' }
  ],
  trainingExperience: [
    { required: true, message: '请输入培训经验', trigger: 'blur' }
  ],
  trainingCapacity: [
    { required: true, message: '请输入培训能力', trigger: 'blur' }
  ],
  trainingPlan: [
    { required: true, message: '请输入培训计划', trigger: 'blur' }
  ],
  teacherInfo: [
    { required: true, message: '请输入师资信息', trigger: 'blur' }
  ]
}

// 所需材料列表
const requiredMaterials = ref([
  {
    name: '机构营业执照或组织机构代码证',
    required: true,
    files: [],
    field: 'qualificationFiles'
  },
  {
    name: '培训计划详细方案',
    required: false,
    files: [],
    field: 'trainingPlanFile'
  },
  {
    name: '师资队伍资质证明材料',
    required: false,
    files: [],
    field: 'teacherCertFiles'
  },
  {
    name: '培训场地及设施设备证明',
    required: false,
    files: [],
    field: 'facilityFiles'
  },
  {
    name: '其他相关资质证明材料',
    required: false,
    files: [],
    field: 'otherFiles'
  }
])

// 查看材料列表（用于详情弹窗）
const viewMaterials = ref([
  {
    name: '机构营业执照或组织机构代码证',
    files: [],
    field: 'qualificationFiles'
  },
  {
    name: '培训计划详细方案',
    files: [],
    field: 'trainingPlanFile'
  },
  {
    name: '师资队伍资质证明材料',
    files: [],
    field: 'teacherCertFiles'
  },
  {
    name: '培训场地及设施设备证明',
    files: [],
    field: 'facilityFiles'
  },
  {
    name: '其他相关资质证明材料',
    files: [],
    field: 'otherFiles'
  }
])

// 生命周期
onMounted(() => {
  loadTrainingList()
})

// 方法
const loadTrainingList = async () => {
  loading.value = true
  try {
    // 获取所有已发布的培训订单
    const trainingResponse = await listTrainingOrder({ orderStatus: '1' })
    const trainings = trainingResponse.rows || []

    // 获取当前用户的机构申请记录
    const applicationResponse = await getMyInstitutionApplications()
    const applications = applicationResponse.data || []

    // 合并培训信息和申请状态
    trainingList.value = trainings.map(training => {
      const institutionApplication = applications.find(app => app.orderId === training.orderId)
      return {
        ...training,
        institutionApplication,
        applicationStatus: institutionApplication?.applicationStatus,
        canApply: !institutionApplication || institutionApplication.applicationStatus === '2' || institutionApplication.applicationStatus === '3'
      }
    })
  } catch (error) {
    ElMessage.error('获取培训列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleApply = (training) => {
  currentTraining.value = training
  applyForm.orderId = training.orderId

  // 如果是重新申请，填充之前的信息
  if (training.institutionApplication) {
    Object.assign(applyForm, {
      institutionName: training.institutionApplication.institutionName,
      institutionCode: training.institutionApplication.institutionCode,
      legalPerson: training.institutionApplication.legalPerson,
      contactPerson: training.institutionApplication.contactPerson,
      contactPhone: training.institutionApplication.contactPhone,
      contactEmail: training.institutionApplication.contactEmail,
      institutionAddress: training.institutionApplication.institutionAddress,
      institutionType: training.institutionApplication.institutionType,
      establishedDate: training.institutionApplication.establishedDate,
      registeredCapital: training.institutionApplication.registeredCapital,
      businessScope: training.institutionApplication.businessScope,
      trainingExperience: training.institutionApplication.trainingExperience,
      trainingCapacity: training.institutionApplication.trainingCapacity,
      trainingPlan: training.institutionApplication.trainingPlan,
      teacherInfo: training.institutionApplication.teacherInfo,
      facilityInfo: training.institutionApplication.facilityInfo,
      applicationNote: ''
    })

    // 填充文件信息
    requiredMaterials.value.forEach(material => {
      const fileData = training.institutionApplication[material.field]
      if (fileData) {
        try {
          let parsedFiles = JSON.parse(fileData) || []
          // 确保文件对象包含所有必要字段
          material.files = parsedFiles.map(file => ({
            name: file.name || file.fileName || file.sourceFileName,
            fileName: file.fileName || file.name || file.sourceFileName,
            sourceFileName: file.sourceFileName || file.name || file.fileName,
            url: file.url || file.filePath,
            filePath: file.filePath || file.url,
            uid: file.uid || new Date().getTime() + Math.random()
          }))
        } catch (error) {
          material.files = []
        }
      } else {
        material.files = []
      }
    })
  } else {
    // 重置表单
    Object.assign(applyForm, {
      orderId: training.orderId,
      institutionName: '',
      institutionCode: '',
      legalPerson: '',
      contactPerson: '',
      contactPhone: '',
      contactEmail: '',
      institutionAddress: '',
      institutionType: '',
      establishedDate: null,
      registeredCapital: null,
      businessScope: '',
      trainingExperience: '',
      trainingCapacity: '',
      trainingPlan: '',
      teacherInfo: '',
      facilityInfo: '',
      applicationNote: ''
    })

    // 重置材料
    resetMaterials()
  }

  // 初始化材料状态
  initMaterials()
  applyDialogVisible.value = true
}

const handleSubmitApply = () => {
  if (!applyFormRef.value) return

  applyFormRef.value.validate(async (valid) => {
    if (valid) {
      // 检查必需材料是否已上传
      const requiredNotUploaded = requiredMaterials.value.filter(m => m.required && (!m.files || m.files.length === 0))
      if (requiredNotUploaded.length > 0) {
        ElMessage.error('请上传所有必需的材料文件')
        return
      }

      submitLoading.value = true

      try {
        // 处理文件数据
        const formDataWithFiles = { ...applyForm }
        requiredMaterials.value.forEach(material => {
          if (material.files && material.files.length > 0) {
            formDataWithFiles[material.field] = JSON.stringify(material.files.map(file => ({
              name: file.name || file.fileName || file.sourceFileName,
              fileName: file.fileName || file.name || file.sourceFileName,
              sourceFileName: file.sourceFileName || file.name || file.fileName,
              url: file.url || file.filePath,
              filePath: file.filePath || file.url
            })))
          }
        })

        // 判断是新申请还是重新申请
        const isReapply = currentTraining.value.institutionApplication

        if (isReapply) {
          // 重新申请 - 使用更新API
          formDataWithFiles.applicationId = currentTraining.value.institutionApplication.applicationId
          formDataWithFiles.applicationStatus = '0' // 重置为待审核状态
          await updateTrainingInstitutionApplicationPublic(formDataWithFiles)
          ElMessage.success('重新申请提交成功，请等待审核')
        } else {
          // 新申请 - 使用提交API
          await submitTrainingInstitutionApplication(formDataWithFiles)
          ElMessage.success('申请提交成功，请等待审核')
        }

        applyDialogVisible.value = false

        // 重新加载培训列表
        await loadTrainingList()
      } catch (error) {
        ElMessage.error(error.msg || '申请提交失败，请稍后重试')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

const handleViewApplication = (application) => {
  currentApplication.value = application

  // 调试：打印申请数据
  console.log('申请数据:', application)

  // 处理文件数据
  viewMaterials.value.forEach(material => {
    const fileData = application[material.field]
    console.log(`${material.name} 文件数据:`, fileData)

    if (fileData) {
      try {
        let parsedFiles = JSON.parse(fileData) || []
        // 确保文件对象包含所有必要字段
        material.files = parsedFiles.map(file => ({
          name: file.name || file.fileName || file.sourceFileName,
          fileName: file.fileName || file.name || file.sourceFileName,
          sourceFileName: file.sourceFileName || file.name || file.fileName,
          url: file.url || file.filePath,
          filePath: file.filePath || file.url
        }))
        console.log(`${material.name} 解析后文件:`, material.files)
      } catch (error) {
        console.error(`${material.name} JSON解析失败:`, error)
        material.files = []
      }
    } else {
      material.files = []
    }
  })

  viewDialogVisible.value = true
}

const handleCancelApplication = async (application) => {
  try {
    await ElMessageBox.confirm('确认要取消申请吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await cancelMyInstitutionApplication(application.applicationId)
    ElMessage.success('取消申请成功')

    // 重新加载培训列表
    await loadTrainingList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.msg || '取消申请失败')
    }
  }
}

const canApplyTraining = (training) => {
  // 检查是否可以申请
  const now = new Date()
  const registrationDeadline = training.registrationDeadline ? new Date(training.registrationDeadline) : null

  // 检查申请截止时间
  if (registrationDeadline && now > registrationDeadline) {
    return false
  }

  return true
}

const getTypeTagType = (trainingType) => {
  const typeMap = {
    '技能培训': 'primary',
    '管理培训': 'success',
    '安全培训': 'warning',
    '专业培训': 'info'
  }
  return typeMap[trainingType] || 'default'
}

const getApplicationStatusTagType = (status) => {
  const statusMap = {
    '0': 'warning',   // 待审核
    '1': 'success',   // 已通过
    '2': 'danger',    // 已拒绝
    '3': 'info'       // 已取消
  }
  return statusMap[status] || 'info'
}

const getApplicationStatusText = (status) => {
  const statusMap = {
    '0': '待审核',
    '1': '已通过',
    '2': '已拒绝',
    '3': '已取消'
  }
  return statusMap[status] || '未知'
}

const getApplicationButtonType = (status) => {
  const typeMap = {
    '0': 'warning',   // 待审核
    '1': 'success',   // 已通过
    '2': 'danger',    // 已拒绝
    '3': 'info'       // 已取消
  }
  return typeMap[status] || 'info'
}

const formatDate = (dateTime) => {
  if (!dateTime) return '--'
  return new Date(dateTime).toLocaleDateString('zh-CN')
}

// 文件上传处理
const handleFileLoad = (data, index) => {
  requiredMaterials.value[index].files = data.fileList || []
}

// 重置材料上传状态
const resetMaterials = () => {
  requiredMaterials.value.forEach(material => {
    material.files = []
  })
}

// 初始化材料状态
const initMaterials = () => {
  requiredMaterials.value.forEach(material => {
    if (!material.files) {
      material.files = []
    }
  })
}
</script>

<style lang="scss" scoped>
.institution-application-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  margin: 0;

  // 独立页面样式
  &.standalone-page {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    overflow-y: auto;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
    z-index: 0;
  }

  >* {
    position: relative;
    z-index: 1;
  }
}

.page-header {
  text-align: center;
  padding: 60px 20px 40px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 0;

  h1 {
    color: #ffffff;
    margin-bottom: 15px;
    font-size: 36px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 1px;
  }

  p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    font-weight: 300;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
}

.training-list {
  padding: 40px 20px;
  max-width: 1400px;
  margin: 0 auto;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;

    h2 {
      color: #ffffff;
      font-size: 28px;
      font-weight: 600;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      margin: 0;
    }

    .list-stats {
      .stats-item {
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
        background: rgba(255, 255, 255, 0.1);
        padding: 8px 16px;
        border-radius: 20px;
        backdrop-filter: blur(10px);
      }
    }
  }
}

.training-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(650px, 1fr));
  gap: 30px;
  padding: 20px 0;

  .empty-state {
    grid-column: 1 / -1;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;

    .empty-content {
      text-align: center;
      color: rgba(255, 255, 255, 0.8);

      .empty-icon {
        margin-bottom: 20px;
        opacity: 0.6;

        .el-icon {
          color: rgba(255, 255, 255, 0.5);
        }
      }

      h3 {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 10px;
        color: rgba(255, 255, 255, 0.9);
      }

      p {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.7);
        margin: 0;
      }
    }
  }
}

.training-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 30px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.4);

    &::before {
      transform: scaleX(1);
    }
  }

  &.has-application::before {
    background: linear-gradient(90deg, #409eff, #36cfc9);
    transform: scaleX(1);
  }

  &.application-approved::before {
    background: linear-gradient(90deg, #67c23a, #95de64);
    transform: scaleX(1);
  }

  &.application-rejected::before {
    background: linear-gradient(90deg, #f56c6c, #ff7875);
    transform: scaleX(1);
  }

  &.application-pending::before {
    background: linear-gradient(90deg, #e6a23c, #ffc53d);
    transform: scaleX(1);
  }

  &.application-cancelled::before {
    background: linear-gradient(90deg, #909399, #bfbfbf);
    transform: scaleX(1);
  }
}

.training-content {
  flex: 1;
}

.training-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  .training-title {
    color: #1a1a1a;
    font-size: 22px;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.training-description {
  color: #4a5568;
  margin-bottom: 20px;
  line-height: 1.6;
  font-size: 15px;
}

.training-info {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;

  .info-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 15px;

    &:last-child {
      margin-bottom: 0;
    }

    .info-item {
      flex: 1;
      min-width: 180px;
      display: flex;
      align-items: center;

      .label {
        font-weight: 600;
        color: #4a5568;
        margin-right: 8px;
        font-size: 14px;
        white-space: nowrap;
      }

      .value {
        color: #2d3748;
        font-weight: 500;
        font-size: 14px;

        &.price {
          color: #e53e3e;
          font-weight: 700;
          font-size: 16px;
        }
      }
    }
  }
}

.training-actions {
  margin-top: 25px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  padding-top: 20px;
  border-top: 1px solid rgba(226, 232, 240, 0.8);

  .action-btn {
    flex: 1;
    min-width: 120px;
    height: 42px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    position: relative;
    overflow: hidden;

    &.primary-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
      }
    }

    &.view-btn {
      background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
      color: white;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
      }
    }

    &.cancel-btn {
      background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
      color: white;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(237, 137, 54, 0.4);
      }
    }
  }

  .status-btn {
    flex: 1;
    min-width: 120px;
    height: 42px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    opacity: 0.8;
    cursor: not-allowed;
  }
}

.apply-form {
  max-height: 70vh;
  overflow-y: auto;
  padding: 10px;

  .form-section {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(248, 250, 252, 0.5);
    border-radius: 8px;
    border: 1px solid rgba(226, 232, 240, 0.8);

    .section-title {
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: #2d3748;
      padding-bottom: 10px;
      border-bottom: 2px solid #667eea;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 600;
    color: #4a5568;
  }

  :deep(.el-input__wrapper) {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  :deep(.el-select .el-input__wrapper) {
    border-radius: 8px;
  }

  :deep(.el-textarea__inner) {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  :deep(.el-date-editor) {
    width: 100%;
  }

  :deep(.el-input-number) {
    width: 100%;
  }
}

// 材料上传样式
.required-materials {
  .material-item {
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border-color: rgba(102, 126, 234, 0.3);
    }

    // 必填材料样式
    &.required-material {
      border-left: 4px solid #f56c6c;
      background: rgba(254, 240, 240, 0.5);

      &:hover {
        border-color: rgba(245, 108, 108, 0.4);
        background: rgba(254, 240, 240, 0.8);
      }
    }

    // 可选材料样式
    &.optional-material {
      border-left: 4px solid #67c23a;
      background: rgba(240, 249, 235, 0.5);

      &:hover {
        border-color: rgba(103, 194, 58, 0.4);
        background: rgba(240, 249, 235, 0.8);
      }
    }

    &:last-child {
      margin-bottom: 0;
    }

    .material-header {
      margin-bottom: 15px;

      .material-info {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .material-name {
          display: flex;
          align-items: center;
          font-weight: 600;
          color: #2d3748;
          font-size: 15px;

          .material-icon {
            margin-right: 8px;
            color: #667eea;
            font-size: 18px;
          }
        }

        .material-status {
          display: flex;
          gap: 8px;
          flex-shrink: 0;
        }
      }
    }

    .material-upload {
      width: 100%;
      padding-left: 26px; // 对齐图标

      .uploaded-files {
        margin-top: 16px;

        .file-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 12px;

          @media (max-width: 768px) {
            grid-template-columns: 1fr;
          }

          .file-card {
            background: rgba(248, 250, 252, 0.9);
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-radius: 8px;
            padding: 8px;
            transition: all 0.3s ease;

            &:hover {
              background: rgba(255, 255, 255, 1);
              border-color: rgba(102, 126, 234, 0.4);
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              transform: translateY(-1px);
            }
          }
        }
      }
    }
  }
}

// 查看详情中的材料展示样式
.uploaded-materials {
  .material-item {
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border-color: rgba(102, 126, 234, 0.3);
    }

    &:last-child {
      margin-bottom: 0;
    }

    .material-header {
      margin-bottom: 15px;

      .material-info {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .material-name {
          display: flex;
          align-items: center;
          font-weight: 600;
          color: #2d3748;
          font-size: 15px;

          .material-icon {
            margin-right: 8px;
            color: #667eea;
            font-size: 18px;
          }
        }

        .material-status {
          display: flex;
          gap: 8px;
          flex-shrink: 0;
        }
      }
    }

    .material-files {
      padding-left: 26px; // 对齐图标

      .file-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-top: 12px;

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
        }

        .file-card {
          background: rgba(248, 250, 252, 0.9);
          border: 1px solid rgba(226, 232, 240, 0.8);
          border-radius: 12px;
          padding: 12px;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 1);
            border-color: rgba(102, 126, 234, 0.4);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
          }
        }
      }
    }
  }
}

// 详情弹窗样式
.application-detail {
  .detail-section {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(248, 250, 252, 0.5);
    border-radius: 8px;
    border: 1px solid rgba(226, 232, 240, 0.8);

    .section-title {
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: #2d3748;
      padding-bottom: 10px;
      border-bottom: 2px solid #667eea;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  :deep(.el-descriptions) {
    .el-descriptions__header {
      margin-bottom: 20px;
    }

    .el-descriptions-item__label {
      font-weight: 600;
      color: #4a5568;
    }
  }
}

.application-detail {
  .el-descriptions {
    margin-top: 20px;

    :deep(.el-descriptions__header) {
      margin-bottom: 20px;
    }

    :deep(.el-descriptions-item__label) {
      font-weight: 600;
      color: #4a5568;
    }
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;

  .el-button {
    border-radius: 8px;
    font-weight: 600;
    padding: 12px 24px;

    &.el-button--primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .training-items {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .training-list {
    padding: 30px 15px;
  }
}

@media (max-width: 768px) {
  .institution-application-container {
    min-height: 100vh;
  }

  .page-header {
    padding: 40px 15px 30px;

    h1 {
      font-size: 28px;
    }

    p {
      font-size: 16px;
    }
  }

  .training-list {
    padding: 20px 10px;

    h2 {
      font-size: 24px;
    }
  }

  .training-items {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 10px 0;
  }

  .training-item {
    padding: 20px;
    border-radius: 12px;

    .training-header .training-title {
      font-size: 18px;
    }
  }

  .training-info {
    padding: 15px;

    .info-row {
      gap: 15px;

      .info-item {
        min-width: 140px;

        .label {
          font-size: 13px;
        }

        .value {
          font-size: 13px;

          &.price {
            font-size: 15px;
          }
        }
      }
    }
  }

  .training-actions {
    gap: 8px;

    .action-btn,
    .status-btn {
      min-width: 100px;
      height: 38px;
      font-size: 13px;
    }
  }

  .apply-form {
    padding: 5px;

    .form-section {
      padding: 15px;
      margin-bottom: 20px;
    }
  }
}

@media (max-width: 480px) {
  .page-header {
    h1 {
      font-size: 24px;
    }

    p {
      font-size: 14px;
    }
  }

  .training-info .info-row {
    flex-direction: column;
    gap: 10px;

    .info-item {
      min-width: auto;
      justify-content: space-between;
    }
  }

  .training-actions {
    flex-direction: column;

    .action-btn,
    .status-btn {
      min-width: auto;
      width: 100%;
    }
  }
}

// 全局弹窗样式优化
:deep(.el-dialog) {
  border-radius: 16px;
  overflow: hidden;

  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;

    .el-dialog__title {
      font-weight: 700;
      font-size: 18px;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 20px;

        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
  }

  .el-dialog__footer {
    padding: 20px 24px;
    background: #f8fafc;
  }
}

// 标签样式优化
:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 600;
  padding: 4px 12px;
  border: none;

  &.el-tag--primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
  }

  &.el-tag--success {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
  }

  &.el-tag--warning {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    color: white;
  }

  &.el-tag--danger {
    background: linear-gradient(135deg, #f56565, #e53e3e);
    color: white;
  }

  &.el-tag--info {
    background: linear-gradient(135deg, #718096, #4a5568);
    color: white;
  }
}

// 加载动画优化
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
}
</style>
