<!DOCTYPE html>
<html>

<head>
    <meta name="keywords" content="西创通 · 西宁市创业服务云平台">
    <meta name="description" content="">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>用工信息详情-西创通 · 西宁市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="../public/css/zh.min.css" />
    <!-- jbox css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/jbox/Skins/Blue/jbox.css" />
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="../public/css/common.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/detail.css?v=202507231048" />
    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />

    <style>
        .detail-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .detail-header {
            border-bottom: 1px solid #e6e6e6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .detail-title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }

        .salary-highlight {
            font-size: 24px;
            font-weight: bold;
            color: #ff6000;
            background: #fff5f0;
            padding: 10px 20px;
            border-radius: 8px;
            display: inline-block;
            margin-bottom: 15px;
        }

        .detail-meta {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-size: 14px;
        }

        .meta-icon {
            font-size: 16px;
        }

        .detail-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .content-main {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .content-sidebar {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            height: fit-content;
        }

        .info-section {
            background: #fff;
            border: 1px solid #e6e6e6;
            border-radius: 8px;
            padding: 20px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #0052d9;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .info-label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }

        .info-value {
            font-size: 14px;
            color: #333;
            font-weight: 600;
        }

        .description-text {
            line-height: 1.6;
            color: #555;
            font-size: 14px;
        }

        .contact-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .contact-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .contact-icon {
            font-size: 16px;
        }

        .urgency-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }

        .urgency-urgent {
            background: #ff4757;
            color: white;
        }

        .urgency-high {
            background: #ff6348;
            color: white;
        }

        .urgency-normal {
            background: #2ed573;
            color: white;
        }

        .urgency-low {
            background: #70a1ff;
            color: white;
        }

        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-published {
            background: #2ed573;
            color: white;
        }

        .status-paused {
            background: #ffa502;
            color: white;
        }

        .status-closed {
            background: #ff4757;
            color: white;
        }

        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            background: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background: #545b62;
            transform: translateY(-1px);
        }

        .apply-btn {
            display: block;
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            text-decoration: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
            margin-bottom: 20px;
        }

        .apply-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .loading-container {
            text-align: center;
            padding: 60px 20px;
        }

        .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0052d9;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .error-container {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .error-icon {
            font-size: 64px;
            color: #e0e0e0;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .detail-content {
                grid-template-columns: 1fr;
            }

            .detail-meta {
                flex-direction: column;
                gap: 10px;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div id="headerBar"></div>

    <!-- 主要内容 -->
    <div class="conAuto2" style="margin-top: 20px; margin-bottom: 40px;">
        <a href="javascript:history.back()" class="back-btn">
            <span>←</span>
            <span>返回列表</span>
        </a>

        <!-- 加载状态 -->
        <div id="loadingContainer" class="loading-container">
            <div class="loading-spinner"></div>
            <p style="margin-top: 15px; color: #666;">正在加载用工信息详情...</p>
        </div>

        <!-- 错误状态 -->
        <div id="errorContainer" class="error-container" style="display: none;">
            <div class="error-icon">💼</div>
            <h3 style="color: #666; margin-bottom: 12px;">用工信息不存在</h3>
            <p style="color: #999; font-size: 14px;">该用工信息可能已下线或不存在</p>
        </div>

        <!-- 详情内容 -->
        <div id="detailContainer" class="detail-container" style="display: none;">
            <div class="detail-header">
                <h1 id="employmentTitle" class="detail-title">用工信息标题</h1>
                <div id="salaryDisplay" class="salary-highlight">面议</div>
                <div class="detail-meta">
                    <div class="meta-item">
                        <span class="meta-icon">🏷️</span>
                        <span id="employmentType">用工类型</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-icon">📂</span>
                        <span id="workCategory">工作类别</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-icon">📍</span>
                        <span id="workLocation">工作地点</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-icon">👁️</span>
                        <span>浏览 <span id="viewCount">0</span> 次</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-icon">📊</span>
                        <span>申请 <span id="applicationCount">0</span> 次</span>
                    </div>
                </div>
            </div>

            <div class="detail-content">
                <div class="content-main">
                    <!-- 基本信息 -->
                    <div class="info-section">
                        <h2 class="section-title">基本信息</h2>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">需要人数</div>
                                <div id="positionsNeeded" class="info-value">--</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">已招聘人数</div>
                                <div id="positionsFilled" class="info-value">--</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">工作时间</div>
                                <div id="workTime" class="info-value">--</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">工作日期</div>
                                <div id="workDate" class="info-value">--</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">学历要求</div>
                                <div id="educationRequired" class="info-value">--</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">经验要求</div>
                                <div id="experienceRequired" class="info-value">--</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">年龄要求</div>
                                <div id="ageRequirement" class="info-value">--</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">性别要求</div>
                                <div id="genderRequired" class="info-value">--</div>
                            </div>
                        </div>
                    </div>

                    <!-- 工作描述 -->
                    <div class="info-section">
                        <h2 class="section-title">工作描述</h2>
                        <div id="workDescription" class="description-text">
                            暂无工作描述
                        </div>
                    </div>

                    <!-- 技能要求 -->
                    <div class="info-section">
                        <h2 class="section-title">技能要求</h2>
                        <div id="skillsRequired" class="description-text">
                            暂无特殊技能要求
                        </div>
                    </div>

                    <!-- 福利待遇 -->
                    <div class="info-section">
                        <h2 class="section-title">福利待遇</h2>
                        <div id="welfareBenefits" class="description-text">
                            暂无福利待遇说明
                        </div>
                    </div>
                </div>

                <div class="content-sidebar">
                    <!-- 申请按钮 -->
                    <a href="javascript:void(0)" class="apply-btn">
                        立即申请
                    </a>

                    <!-- 联系信息 -->
                    <div class="contact-info">
                        <div class="contact-title">联系信息</div>
                        <div class="contact-item">
                            <span class="contact-icon">👤</span>
                            <span>联系人：<span id="contactPerson">--</span></span>
                        </div>
                        <div class="contact-item">
                            <span class="contact-icon">📞</span>
                            <span>电话：<span id="contactPhone">--</span></span>
                        </div>
                        <div class="contact-item">
                            <span class="contact-icon">✉️</span>
                            <span>邮箱：<span id="contactEmail">--</span></span>
                        </div>
                    </div>

                    <!-- 公司信息 -->
                    <div class="info-section">
                        <h3 class="section-title">公司信息</h3>
                        <div class="info-item" style="margin-bottom: 10px;">
                            <div class="info-label">公司名称</div>
                            <div id="companyName" class="info-value">--</div>
                        </div>
                        <div class="info-item" style="margin-bottom: 10px;">
                            <div class="info-label">公司地址</div>
                            <div id="companyAddress" class="info-value">--</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">公司描述</div>
                            <div id="companyDescription" class="description-text"
                                style="font-size: 12px; margin-top: 5px;">--</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部 -->
    <div id="footerBar"></div>

    <!--jquery js-->
    <script src="../public/js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <!--common js-->
    <script src="../public/js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/common.js" type="text/javascript" charset="utf-8"></script>

    <script>
        // 公用模块html
        headerBar();
        footerBar();

        // 获取URL参数
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // 原生Ajax请求函数
        function laborAjaxRequest(url, params, callback) {
            // var baseUrl = 'http://localhost:80/sux-admin/';
            var baseUrl = 'http://************/sux-admin/';

            // 构建查询参数
            var queryString = '';
            if (params && typeof params === 'object') {
                var paramArray = [];
                for (var key in params) {
                    if (params.hasOwnProperty(key) && params[key] !== null && params[key] !== undefined) {
                        paramArray.push(encodeURIComponent(key) + '=' + encodeURIComponent(params[key]));
                    }
                }
                queryString = paramArray.length > 0 ? '?' + paramArray.join('&') : '';
            }

            var xhr = new XMLHttpRequest();
            xhr.open('GET', baseUrl + url + queryString, true);
            xhr.timeout = 30000;
            xhr.setRequestHeader('Content-Type', 'application/json');

            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (callback && typeof callback === 'function') {
                                callback(response);
                            }
                        } catch (e) {
                            console.error('解析响应数据失败:', e);
                            if (callback && typeof callback === 'function') {
                                callback({
                                    code: -1,
                                    msg: '解析响应数据失败',
                                    data: null
                                });
                            }
                        }
                    } else {
                        console.error('请求失败:', xhr.status, xhr.statusText);
                        if (callback && typeof callback === 'function') {
                            callback({
                                code: -1,
                                msg: '请求失败: ' + xhr.status + ' ' + xhr.statusText,
                                data: null
                            });
                        }
                    }
                }
            };

            xhr.ontimeout = function () {
                console.error('请求超时');
                if (callback && typeof callback === 'function') {
                    callback({
                        code: -1,
                        msg: '请求超时',
                        data: null
                    });
                }
            };

            xhr.onerror = function () {
                console.error('请求发生错误');
                if (callback && typeof callback === 'function') {
                    callback({
                        code: -1,
                        msg: '网络错误',
                        data: null
                    });
                }
            };

            xhr.send();
        }

        // 获取用工信息详情
        function loadEmploymentDetail(employmentId) {
            laborAjaxRequest('public/labor/employments/' + employmentId, {}, function (response) {
                if (response.code == 0 || response.code == 200) {
                    var employment = response.data;
                    renderEmploymentDetail(employment);
                } else {
                    showError();
                }
            });
        }

        // 渲染用工信息详情
        function renderEmploymentDetail(employment) {
            $('#employmentTitle').text(employment.title || '未知用工信息');

            // 格式化薪资显示
            var salaryText = '面议';
            if (employment.salaryMin && employment.salaryMax) {
                var salaryUnit = getSalaryTypeDisplayName(employment.salaryType);
                salaryText = '￥' + employment.salaryMin + '-' + employment.salaryMax + '/' + salaryUnit;
            } else if (employment.salaryMin) {
                var salaryUnit = getSalaryTypeDisplayName(employment.salaryType);
                salaryText = '￥' + employment.salaryMin + '+/' + salaryUnit;
            }
            $('#salaryDisplay').text(salaryText);

            $('#employmentType').text(employment.employmentType || '--');
            $('#workCategory').text(employment.workCategory || '--');
            $('#workLocation').text(employment.workLocation || '--');
            $('#viewCount').text(employment.viewCount || 0);
            $('#applicationCount').text(employment.applicationCount || 0);

            $('#positionsNeeded').text(employment.positionsNeeded ? employment.positionsNeeded + '人' : '--');
            $('#positionsFilled').text(employment.positionsFilled ? employment.positionsFilled + '人' : '--');

            // 工作时间
            var workTimeText = '--';
            if (employment.workHoursPerDay && employment.workDaysPerWeek) {
                workTimeText = employment.workHoursPerDay + '小时/天，' + employment.workDaysPerWeek + '天/周';
            }
            $('#workTime').text(workTimeText);

            // 工作日期
            var workDateText = '--';
            if (employment.startDate && employment.endDate) {
                workDateText = employment.startDate + ' 至 ' + employment.endDate;
            } else if (employment.startDate) {
                workDateText = '从 ' + employment.startDate + ' 开始';
            }
            $('#workDate').text(workDateText);

            $('#educationRequired').text(employment.educationRequired || '--');
            $('#experienceRequired').text(employment.experienceRequired || '--');

            // 年龄要求
            var ageText = '--';
            if (employment.ageMin && employment.ageMax) {
                ageText = employment.ageMin + '-' + employment.ageMax + '岁';
            } else if (employment.ageMin) {
                ageText = employment.ageMin + '岁以上';
            }
            $('#ageRequirement').text(ageText);

            var genderText = employment.genderRequired || '--';
            if (genderText === 'male') genderText = '男';
            else if (genderText === 'female') genderText = '女';
            else if (genderText === 'unlimited') genderText = '不限';
            $('#genderRequired').text(genderText);

            $('#workDescription').text(employment.workDescription || '暂无工作描述');
            $('#skillsRequired').text(JSON.parse(employment.skillsRequired) || '暂无特殊技能要求');
            $('#welfareBenefits').text(employment.welfareBenefits || '暂无福利待遇说明');

            $('#contactPerson').text(employment.contactPerson || '--');
            $('#contactPhone').text(employment.contactPhone || '--');
            $('#contactEmail').text(employment.contactEmail || '--');

            $('#companyName').text(employment.companyName || '--');
            $('#companyAddress').text(employment.companyAddress || '--');
            $('#companyDescription').text(employment.companyDescription || '--');

            // 显示详情容器
            $('#loadingContainer').hide();
            $('#detailContainer').show();
        }

        // 获取薪资类型显示名称
        function getSalaryTypeDisplayName(type) {
            var displayNames = {
                'hourly': '小时',
                'daily': '天',
                'monthly': '月',
                'piece': '件'
            };
            return displayNames[type] || type || '月';
        }

        // 申请工作
        function applyJob() {
            alert('申请功能需要登录后使用');
        }

        // 显示错误信息
        function showError() {
            $('#loadingContainer').hide();
            $('#errorContainer').show();
        }

        // 页面初始化
        $(document).ready(function () {
            var employmentId = getUrlParameter('id');
            if (employmentId) {
                loadEmploymentDetail(employmentId);
            } else {
                showError();
            }
        });
    </script>
</body>

</html>