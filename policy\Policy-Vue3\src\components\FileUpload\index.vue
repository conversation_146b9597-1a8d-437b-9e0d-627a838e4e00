<template>
  <div class="upload-file">
    <el-upload multiple :action="uploadFileUrl + fileUrl" :before-upload="handleBeforeUpload" :file-list="fileList"
      :limit="limit" :on-error="handleUploadError" :on-exceed="handleExceed" :on-success="handleUploadSuccess"
      :show-file-list="false" :headers="headers" :data="fileData" class="upload-file-uploader" ref="fileUpload">
      <!-- 上传按钮 -->
      <el-button type="primary" class="custom-btn">选取文件</el-button>
    </el-upload>
    <!-- 上传提示 -->
    <div class="el-upload__tip" v-if="showTip">
      请上传
      <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
      <template v-if="fileType && fileType.length"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
      </template>
      的文件
      <span v-if="temp && temp.tempType">
        ，
        <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline"
          @click="importTemplate">下载模板</el-link></span>
    </div>
    <!-- 文件列表 -->
    <transition-group class="upload-file-list" name="el-fade-in-linear" tag="ul" v-if="fileList.length > 0">
      <li :key="file.uid" class="file-item" v-for="(file, index) in fileList">
        <div class="file-item-content">
          <FileView :file="file" />
          <div class="file-action">
            <el-button type="danger" size="small" icon="Delete" circle @click="handleDelete(index)" />
          </div>
        </div>
      </li>
    </transition-group>

    <!-- 空状态提示 -->
    <div v-if="fileList.length === 0" class="empty-state">
      <el-icon class="empty-icon"><Document /></el-icon>
      <p class="empty-text">暂无文件，点击上方按钮上传</p>
    </div>
  </div>
</template>

<script setup name="FileUpload">
import {
  getToken
} from "@/utils/auth";
import { ref, computed, watch, getCurrentInstance } from 'vue';
import { Document } from '@element-plus/icons-vue';
import FileView from '@/components/FileView/index.vue';

const props = defineProps({
  // 值
  value: [String, Object, Array],
  // 数量限制
  limit: {
    type: Number,
    default: 1,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5,
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => [],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true
  },
  //上传路劲
  fileUrl: {
    type: String,
    default: "/common/upload"
  },
  fileData: {
    type: Object,
    default: () => ({
      fileBizType: 'sys_defualt_file_upload'
    })
  },
  temp: {
    type: Object,
    default: () => ({
      tempType: false
    }),
  }
});

const {
  proxy
} = getCurrentInstance();
const emit = defineEmits(['fileLoad', 'update:value']);
const number = ref(0);
const uploadList = ref([]);
const fielUrl = import.meta.env.VITE_APP_FILE_URL;
const uploadFileUrl = import.meta.env.VITE_APP_BASE_API; // 上传文件服务器地址
const headers = ref({
  'ADMIN-Authorization': "Bearer " + getToken()
});
const fileList = ref([]);
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize)
);

watch(() => props.value, val => {
  if (val) {
    let temp = 1;
    let list = [];

    // 处理不同格式的输入数据
    if (Array.isArray(val)) {
      list = val;
    } else if (typeof val === 'string') {
      try {
        // 尝试解析JSON字符串
        list = JSON.parse(val);
        if (!Array.isArray(list)) {
          // 如果不是数组，按逗号分割
          list = val.split(',').filter(item => item.trim());
        }
      } catch (error) {
        // JSON解析失败，按逗号分割
        list = val.split(',').filter(item => item.trim());
      }
    }

    // 将数组转为标准对象数组
    fileList.value = list.map(item => {
      if (typeof item === "string") {
        item = {
          name: getFileName(item),
          fileName: getFileName(item),
          sourceFileName: getFileName(item),
          filePath: item,
          url: item
        };
      } else if (typeof item === "object" && item !== null) {
        // 确保对象包含所有必要字段
        item = {
          name: item.name || item.fileName || item.sourceFileName || getFileName(item.filePath || item.url),
          fileName: item.fileName || item.name || item.sourceFileName || getFileName(item.filePath || item.url),
          sourceFileName: item.sourceFileName || item.name || item.fileName || getFileName(item.filePath || item.url),
          filePath: item.filePath || item.url,
          url: item.url || item.filePath,
          ...item // 保留其他字段
        };
      }
      item.uid = item.uid || new Date().getTime() + temp++;
      return item;
    });
  } else {
    fileList.value = [];
    return [];
  }
}, {
  deep: true,
  immediate: true
});

// 上传前校检格式和大小
function handleBeforeUpload(file) {
  // 校检文件类型
  if (props.fileType && props.fileType.length) {
    const fileName = file.name.split('.');
    const fileExt = fileName[fileName.length - 1]?.toLowerCase();
    const isTypeOk = props.fileType.some(type => type.toLowerCase() === fileExt);
    if (!isTypeOk) {
      proxy.$modal.msgError(`文件格式不正确，请上传 ${props.fileType.join("/")} 格式的文件！`);
      return false;
    }
  }

  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB！`);
      return false;
    }
  }

  // 检查文件数量限制
  if (fileList.value.length >= props.limit) {
    proxy.$modal.msgError(`最多只能上传 ${props.limit} 个文件！`);
    return false;
  }

  proxy.$modal.loading(`正在上传文件 "${file.name}"，请稍候...`);
  number.value++;
  return true;
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个！`);
}

// 上传失败
function handleUploadError(err, file) {
  const fileName = file?.name || '文件';
  proxy.$modal.msgError(`上传文件 "${fileName}" 失败，请重试！`);
  proxy.$modal.closeLoading();
  number.value--;
  console.error('文件上传失败:', err);
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.code === 200) {
    // 统一文件数据结构，确保包含所有必要字段
    const fileData = {
      name: res.newFileName || file.name,
      fileName: res.newFileName || file.name,
      sourceFileName: res.originalFilename || file.name,
      filePath: res.filePath,
      url: res.filePath, // 添加url字段以保持兼容性
      uid: file.uid || new Date().getTime()
    };

    uploadList.value.push(fileData);
    uploadedSuccessfully();
  } else {
    number.value--;
    proxy.$modal.closeLoading();
    const errorMsg = res.msg || `上传文件 "${file.name}" 失败`;
    proxy.$modal.msgError(errorMsg);

    // 从上传组件中移除失败的文件
    if (proxy.$refs.fileUpload) {
      proxy.$refs.fileUpload.handleRemove(file);
    }

    // 如果所有文件都处理完了，也要调用成功回调来关闭loading
    if (number.value === 0) {
      uploadedSuccessfully();
    }
  }
}

// 删除文件
function handleDelete(index) {
  fileList.value.splice(index, 1);
  emit("fileLoad", {
    fileList: fileList.value,
    type: "del"
  });
  emit("update:value", fileList.value);
}

// 全部删除文件
function handleAllDelete(index) {
  fileList.value = [];
  emit("fileLoad", {
    fileList: fileList.value,
    type: "del"
  });
  emit("update:value", fileList.value);
};

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    const newFiles = uploadList.value;
    fileList.value = fileList.value.filter(f => f.fileId !== undefined).concat(newFiles);

    // 显示成功提示
    if (newFiles.length === 1) {
      proxy.$modal.msgSuccess(`文件 "${newFiles[0].sourceFileName}" 上传成功！`);
    } else if (newFiles.length > 1) {
      proxy.$modal.msgSuccess(`成功上传 ${newFiles.length} 个文件！`);
    }

    uploadList.value = [];
    number.value = 0;

    emit("fileLoad", {
      fileList: fileList.value,
      type: "add"
    });
    emit("update:value", fileList.value);
    proxy.$modal.closeLoading();
  } else if (number.value === 0) {
    // 如果没有待上传的文件了，也要关闭loading
    proxy.$modal.closeLoading();
  }
}

// 获取文件名称
function getFileName(name) {
  // 如果是url那么取最后的名字 如果不是直接返回
  if (name.lastIndexOf("/") > -1) {
    return name.slice(name.lastIndexOf("/") + 1);
  } else {
    return name;
  }
}

// 对象转成指定字符串分隔
function listToString(list, separator) {
  let strs = "";
  separator = separator || ",";
  for (let i in list) {
    if (list[i].url) {
      strs += list[i].url + separator;
    }
  }
  return strs != '' ? strs.substr(0, strs.length - 1) : '';
}

//下载模板
function importTemplate() {
  if (props.temp && props.temp.type && props.temp.name) {
    proxy.download('monitor/open/excelTemplate/importTemplate', {
      type: props.temp.type
    }, `${props.temp.name}_${new Date().getTime()}.xlsx`)
  }
}

defineExpose({
  handleAllDelete,
});
</script>

<style scoped lang="scss">
.upload-file {
  width: 100%;
}

.upload-file-uploader {
  margin-bottom: 16px;

  :deep(.el-upload) {
    width: 100%;
  }

  .custom-btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    }
  }
}

.el-upload__tip {
  margin-top: 8px;
  color: #606266;
  font-size: 13px;
  line-height: 1.5;

  b {
    font-weight: 600;
  }
}

.upload-file-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  padding: 0;
  margin-top: 16px;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
  }
}

.file-item {
  list-style: none;
  position: relative;
  background: #fafafa;
  border-radius: 8px;
  padding: 8px;
  transition: all 0.3s ease;

  &:hover {
    background: #f0f9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .file-action {
      opacity: 1;
    }
  }
}

.file-item-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 100%;
}

.file-action {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease;

  .el-button {
    padding: 4px;
    font-size: 12px;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);

    &:hover {
      transform: scale(1.1);
    }
  }
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
  background: #fafafa;
  border: 2px dashed #e4e7ed;
  border-radius: 8px;
  margin-top: 16px;
  transition: all 0.3s ease;

  &:hover {
    border-color: #c0c4cc;
    background: #f5f7fa;
  }

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: #c0c4cc;
  }

  .empty-text {
    margin: 0;
    font-size: 14px;
    color: #909399;
  }
}

// 加载状态样式
:deep(.el-loading-mask) {
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(2px);
}

// 上传按钮样式优化
:deep(.el-button--primary) {
  background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%);
  border: none;

  &:hover {
    background: linear-gradient(135deg, #66b1ff 0%, #5cdbd3 100%);
  }

  &:active {
    background: linear-gradient(135deg, #3a8ee6 0%, #2bb0a8 100%);
  }
}
</style>
